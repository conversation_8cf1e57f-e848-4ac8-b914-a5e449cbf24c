[package]
name = "achidas"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web framework and async runtime
tokio = { version = "1.0", features = ["full"] }
axum = { version = "0.7", features = ["macros", "tower-log"] }
tower = { version = "0.4", features = ["full"] }
tower-http = { version = "0.5", features = ["cors", "trace", "timeout"] }
hyper = { version = "1.0", features = ["full"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# HTTP client
reqwest = { version = "0.11", features = ["json", "rustls-tls"] }

# Database
mongodb = "2.8"
bson = { version = "2.9", features = ["chrono-0_4"] }

# Authentication and security
jsonwebtoken = "9.2"
bcrypt = "0.15"
uuid = { version = "1.7", features = ["v4", "serde"] }

# Configuration
config = "0.14"
dotenvy = "0.15"

# Observability and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-opentelemetry = "0.22"
opentelemetry = { version = "0.21", features = ["trace"] }
opentelemetry_sdk = { version = "0.21", features = ["trace", "rt-tokio"] }
opentelemetry-jaeger = { version = "0.20", features = ["rt-tokio"] }
metrics = "0.22"
metrics-exporter-prometheus = "0.13"

# Retry logic and resilience
backoff = { version = "0.4", features = ["tokio"] }
circuit-breaker = "0.1"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Validation
validator = { version = "0.18", features = ["derive"] }

# Environment
once_cell = "1.19"

# Async utilities
futures = "0.3"
async-trait = "0.1"

[dev-dependencies]
tokio-test = "0.4"
