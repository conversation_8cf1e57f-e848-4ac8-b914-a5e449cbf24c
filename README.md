# Achidas - Whitelabel Hosting Platform Backend

A comprehensive Rust backend for a whitelabel hosting platform similar to Render.com, built with modern async Rust and integrating with the Vultr API.

## Features

### 🚀 Core Functionality
- **User Management**: Registration, authentication, and profile management
- **Instance Management**: Create, manage, and monitor cloud instances via Vultr API
- **Billing System**: Usage tracking, invoicing, and billing management
- **Admin Panel**: Administrative controls and system monitoring

### 🏗️ Architecture
- **MVC Framework**: Clean separation of concerns with controllers, models, and services
- **Async Runtime**: Built on Tokio for high-performance concurrency
- **Database**: MongoDB integration with proper indexing
- **API Integration**: Robust Vultr API client with retry logic

### 🔧 Observability & Resilience
- **Distributed Tracing**: OpenTelemetry integration with Jaeger
- **Metrics**: Prometheus metrics collection
- **Retry Logic**: Exponential backoff with jitter for external API calls
- **Circuit Breakers**: Built-in resilience patterns
- **Structured Logging**: Comprehensive logging with tracing

### 🛡️ Security & Authentication
- **JWT Authentication**: Secure token-based authentication
- **Role-based Access Control**: Admin and user roles
- **Password Hashing**: bcrypt for secure password storage
- **Input Validation**: Comprehensive request validation

## Tech Stack

- **Framework**: Axum (high-performance async web framework)
- **Runtime**: Tokio (async runtime)
- **Database**: MongoDB with official Rust driver
- **HTTP Client**: reqwest with custom retry logic
- **Authentication**: JWT with jsonwebtoken
- **Observability**: OpenTelemetry + Jaeger + Prometheus
- **Validation**: validator crate
- **Serialization**: serde with JSON support

## Project Structure

```
src/
├── main.rs                 # Application entry point
├── lib.rs                  # Library exports
├── config/                 # Configuration management
├── controllers/            # HTTP request handlers
│   ├── auth.rs            # Authentication endpoints
│   ├── instances.rs       # Instance management
│   ├── billing.rs         # Billing endpoints
│   ├── users.rs           # User management
│   ├── vultr.rs           # Vultr resource endpoints
│   └── admin.rs           # Admin endpoints
├── models/                 # Data models and DTOs
│   ├── user.rs            # User models
│   ├── instance.rs        # Instance models
│   └── billing.rs         # Billing models
├── services/               # Business logic layer
│   ├── auth.rs            # Authentication service
│   ├── instance.rs        # Instance management service
│   ├── billing.rs         # Billing service
│   ├── user.rs            # User service
│   └── admin.rs           # Admin service
├── routes/                 # Route definitions
├── middleware/             # Custom middleware
│   └── auth.rs            # Authentication middleware
├── vultr/                  # Vultr API client
│   ├── client.rs          # API client implementation
│   └── models.rs          # Vultr API models
├── database/               # Database connection and utilities
├── observability/          # Tracing and metrics setup
└── utils/                  # Utility functions
    └── retry.rs           # Retry logic with exponential backoff
```

## API Endpoints

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh JWT token

### Instance Management
- `GET /api/v1/instances` - List user instances
- `POST /api/v1/instances` - Create new instance
- `GET /api/v1/instances/:id` - Get instance details
- `PUT /api/v1/instances/:id` - Update instance
- `DELETE /api/v1/instances/:id` - Delete instance
- `POST /api/v1/instances/:id/start` - Start instance
- `POST /api/v1/instances/:id/stop` - Stop instance
- `POST /api/v1/instances/:id/restart` - Restart instance

### Vultr Resources
- `GET /api/v1/vultr/account` - Get account information
- `GET /api/v1/vultr/account/bgp` - Get account BGP information
- `GET /api/v1/vultr/account/bandwidth` - Get account bandwidth information
- `GET /api/v1/vultr/plans` - List available plans
- `GET /api/v1/vultr/regions` - List available regions
- `GET /api/v1/vultr/os` - List available operating systems

### Billing
- `GET /api/v1/billing` - Get billing information
- `GET /api/v1/billing/invoices` - List invoices
- `GET /api/v1/billing/invoices/:id` - Get invoice details
- `GET /api/v1/billing/usage` - Get usage data

### User Management
- `GET /api/v1/users/profile` - Get user profile
- `PUT /api/v1/users/profile` - Update user profile

### Admin (Admin only)
- `GET /api/v1/admin/users` - List all users
- `GET /api/v1/admin/users/:id` - Get user details
- `POST /api/v1/admin/users/:id/suspend` - Suspend user
- `POST /api/v1/admin/users/:id/activate` - Activate user
- `GET /api/v1/admin/instances` - List all instances
- `GET /api/v1/admin/billing/overview` - Billing overview
- `GET /api/v1/admin/metrics` - System metrics

## Setup and Installation

### Prerequisites
- Rust 1.70+ 
- MongoDB 4.4+
- Vultr API key

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd achidas
```

2. **Set up environment variables**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Install dependencies**
```bash
cargo build
```

4. **Run the application**
```bash
cargo run
```

### Environment Variables

```env
# Server Configuration
SERVER_ADDRESS=0.0.0.0:3000

# Database Configuration
DATABASE_URL=mongodb://localhost:27017/achidas

# Vultr API Configuration
VULTR_API_KEY=your_vultr_api_key_here

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here

# Observability Configuration
JAEGER_ENDPOINT=http://localhost:14268/api/traces
PROMETHEUS_ENDPOINT=0.0.0.0:9090
LOG_LEVEL=info
```

## Development

### Running Tests
```bash
cargo test
```

### Running with Debug Logging
```bash
RUST_LOG=debug cargo run
```

### Database Setup
The application will automatically create necessary indexes on startup. Make sure MongoDB is running and accessible.

## Monitoring and Observability

### Metrics
Prometheus metrics are available at `http://localhost:9090/metrics` (configurable)

Key metrics include:
- HTTP request counts and durations
- Vultr API call counts and errors
- Active instance counts
- User registration and login counts

### Tracing
Distributed tracing is available via OpenTelemetry and Jaeger. Configure the `JAEGER_ENDPOINT` environment variable to enable.

### Logging
Structured logging with configurable levels. Set `LOG_LEVEL` environment variable to control verbosity.

## Production Deployment

### Docker Support
```dockerfile
# Dockerfile example
FROM rust:1.70 as builder
WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/achidas /usr/local/bin/achidas
EXPOSE 3000
CMD ["achidas"]
```

### Kubernetes Deployment
The application is designed to be stateless and can be easily deployed to Kubernetes with proper configuration management.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

[Add your license here]

## Support

For support and questions, please [create an issue](link-to-issues) or contact the development team.
