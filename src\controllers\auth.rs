use crate::{
    controllers::{success_response, <PERSON><PERSON><PERSON><PERSON>, ControllerR<PERSON><PERSON>},
    models::{CreateUserRequest, LoginRequest, LoginResponse, UserProfile},
    services::auth::AuthService,
    AppState,
};
use axum::{extract::State, Json};
use std::sync::Arc;
use tracing::instrument;
use validator::Validate;

#[instrument(skip(state, request))]
pub async fn register(
    State(state): State<Arc<AppState>>,
    <PERSON>son(request): Json<CreateUserRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<UserProfile>>> {
    // Validate request
    request.validate().map_err(|e| {
        ControllerError::Validation(format!("Validation failed: {}", e))
    })?;

    let auth_service = AuthService::new(&state.database, &state.config);
    let user_profile = auth_service.register(request).await?;

    Ok(success_response(user_profile))
}

#[instrument(skip(state, request))]
pub async fn login(
    State(state): State<Arc<AppState>>,
    <PERSON><PERSON>(request): <PERSON><PERSON><LoginRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<LoginResponse>>> {
    // Validate request
    request.validate().map_err(|e| {
        ControllerError::Validation(format!("Validation failed: {}", e))
    })?;

    let auth_service = AuthService::new(&state.database, &state.config);
    let login_response = auth_service.login(request).await?;

    Ok(success_response(login_response))
}

#[instrument(skip(state))]
pub async fn refresh_token(
    State(state): State<Arc<AppState>>,
    // TODO: Extract refresh token from request
) -> ControllerResult<Json<crate::models::ApiResponse<LoginResponse>>> {
    // TODO: Implement refresh token logic
    Err(ControllerError::Internal("Not implemented".to_string()))
}
