use crate::{
    controllers::{success_response, <PERSON><PERSON><PERSON><PERSON>, ControllerR<PERSON><PERSON>},
    middleware::auth::get_current_user,
    models::{CreateInstanceRequest, InstanceResponse, PaginatedResponse, PaginationQuery},
    services::instance::InstanceService,
    AppState,
};
use axum::{
    extract::{Path, Query, Request, State},
    Json,
};
use std::sync::Arc;
use tracing::instrument;
use validator::Validate;

#[instrument(skip(state, req))]
pub async fn list_instances(
    State(state): State<Arc<AppState>>,
    Query(pagination): Query<PaginationQuery>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<PaginatedResponse<InstanceResponse>>>> {
    let claims = get_current_user(&req)?;
    
    pagination.validate().map_err(|e| {
        ControllerError::Validation(format!("Validation failed: {}", e))
    })?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    let instances = instance_service
        .list_user_instances(&claims.sub, pagination)
        .await?;

    Ok(success_response(instances))
}

#[instrument(skip(state, req))]
pub async fn get_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<InstanceResponse>>> {
    let claims = get_current_user(&req)?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    let instance = instance_service
        .get_user_instance(&claims.sub, &instance_id)
        .await?;

    Ok(success_response(instance))
}

#[instrument(skip(state, req, request))]
pub async fn create_instance(
    State(state): State<Arc<AppState>>,
    req: Request,
    Json(request): Json<CreateInstanceRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<InstanceResponse>>> {
    let claims = get_current_user(&req)?;
    
    request.validate().map_err(|e| {
        ControllerError::Validation(format!("Validation failed: {}", e))
    })?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    let instance = instance_service
        .create_instance(&claims.sub, request)
        .await?;

    Ok(success_response(instance))
}

#[instrument(skip(state, req))]
pub async fn update_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    req: Request,
    Json(request): Json<serde_json::Value>,
) -> ControllerResult<Json<crate::models::ApiResponse<InstanceResponse>>> {
    let claims = get_current_user(&req)?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    let instance = instance_service
        .update_instance(&claims.sub, &instance_id, request)
        .await?;

    Ok(success_response(instance))
}

#[instrument(skip(state, req))]
pub async fn delete_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    let claims = get_current_user(&req)?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    instance_service
        .delete_instance(&claims.sub, &instance_id)
        .await?;

    Ok(success_response(()))
}

#[instrument(skip(state, req))]
pub async fn start_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    let claims = get_current_user(&req)?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    instance_service
        .start_instance(&claims.sub, &instance_id)
        .await?;

    Ok(success_response(()))
}

#[instrument(skip(state, req))]
pub async fn stop_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    let claims = get_current_user(&req)?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    instance_service
        .stop_instance(&claims.sub, &instance_id)
        .await?;

    Ok(success_response(()))
}

#[instrument(skip(state, req))]
pub async fn restart_instance(
    State(state): State<Arc<AppState>>,
    Path(instance_id): Path<String>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    let claims = get_current_user(&req)?;

    let instance_service = InstanceService::new(&state.database, &state.vultr_client);
    instance_service
        .restart_instance(&claims.sub, &instance_id)
        .await?;

    Ok(success_response(()))
}
