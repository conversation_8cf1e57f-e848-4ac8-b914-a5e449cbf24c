use crate::{
    controllers::{success_response, ControllerResult},
    middleware::auth::get_current_user,
    models::UserProfile,
    services::user::UserService,
    AppState,
};
use axum::{extract::{Request, State}, J<PERSON>};
use std::sync::Arc;
use tracing::instrument;

#[instrument(skip(state, req))]
pub async fn get_profile(
    State(state): State<Arc<AppState>>,
    req: Request,
) -> ControllerResult<Json<crate::models::ApiResponse<UserProfile>>> {
    let claims = get_current_user(&req)?;

    let user_service = UserService::new(&state.database);
    let user_profile = user_service.get_user_profile(&claims.sub).await?;

    Ok(success_response(user_profile))
}

#[instrument(skip(state, req, update_data))]
pub async fn update_profile(
    State(state): State<Arc<AppState>>,
    req: Request,
    Json(update_data): Json<serde_json::Value>,
) -> ControllerResult<Json<crate::models::ApiResponse<UserProfile>>> {
    let claims = get_current_user(&req)?;

    let user_service = UserService::new(&state.database);
    let user_profile = user_service
        .update_user_profile(&claims.sub, update_data)
        .await?;

    Ok(success_response(user_profile))
}
