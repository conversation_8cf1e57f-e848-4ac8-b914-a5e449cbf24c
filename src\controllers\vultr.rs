use crate::{
    controllers::{success_response, ControllerResult},
    models::{
        BareMetalBandwidth, BareMetalIpv4Info, BareMetalIpv6Info, BareMetalUpgrades,
        BareMetalUserData, BareMetalVncInfo, BareMetalVpcInfo, CreateBareMetalRequest,
        UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
    },
    vultr::models::{
        VultrSSHKey, VultrAccount, VultrAccountBGP, VultrAccountBandwidth,
        VultrBlockStorage, CreateBlockStorageRequest, UpdateBlockStorageRequest,
        AttachBlockStorageRequest, DetachBlockStorageRequest, VultrInvoiceItem,
        VultrPendingCharges, VultrPullZone, CreatePullZoneRequest, UpdatePullZoneRequest,
        PurgePullZoneRequest, VultrPushZone, CreatePushZoneRequest, UpdatePushZoneRequest,
        <PERSON><PERSON>rPushZoneFile, VultrContainerRegistry, CreateRegistryRequest, UpdateRegistryRequest,
        VultrRegistryReplication, CreateReplicationRequest, VultrRegistryRepository,
        UpdateRepositoryRequest, VultrDockerCredentials, VultrKubernetesDockerCredentials,
        UpdateRegistryPasswordRequest, VultrRegistryRobot, UpdateRobotRequest,
        VultrRegistryArtifact, VultrRegistryRegion, VultrRegistryPlan,
        VultrDNSDomainDetailed, CreateDNSDomainRequest, UpdateDNSDomainRequest,
        VultrDNSSOA, UpdateDNSSOARequest, VultrDNSRecordDetailed, CreateDNSRecordRequest,
        UpdateDNSRecordRequest, VultrFirewallGroupDetailed, CreateFirewallGroupRequest,
        UpdateFirewallGroupRequest, VultrFirewallRuleDetailed, CreateFirewallRuleRequest,
    },
    AppState,
};
use axum::{
    extract::{Path, State},
    Json,
};
use std::sync::Arc;
use tracing::instrument;

// Account endpoints
#[instrument(skip(state))]
pub async fn get_account(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrAccount>>> {
    let account = state
        .vultr_client
        .get_account()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(account))
}

#[instrument(skip(state))]
pub async fn get_account_bgp(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrAccountBGP>>> {
    let bgp_info = state
        .vultr_client
        .get_account_bgp()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bgp_info))
}

#[instrument(skip(state))]
pub async fn get_account_bandwidth(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrAccountBandwidth>>> {
    let bandwidth_info = state
        .vultr_client
        .get_account_bandwidth()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bandwidth_info))
}

#[instrument(skip(state))]
pub async fn list_plans(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrPlan>>>> {
    let plans = state
        .vultr_client
        .list_plans()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    // Convert to our model format
    let converted_plans: Vec<VultrPlan> = plans
        .into_iter()
        .map(|plan| VultrPlan {
            id: plan.id,
            vcpu_count: plan.vcpu_count,
            ram: plan.ram,
            disk: plan.disk,
            bandwidth: plan.bandwidth,
            monthly_cost: plan.monthly_cost,
            type_: plan.plan_type,
            locations: plan.locations,
        })
        .collect();

    Ok(success_response(converted_plans))
}

#[instrument(skip(state))]
pub async fn list_regions(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegion>>>> {
    let regions = state
        .vultr_client
        .list_regions()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    // Convert to our model format
    let converted_regions: Vec<VultrRegion> = regions
        .into_iter()
        .map(|region| VultrRegion {
            id: region.id,
            city: region.city,
            country: region.country,
            continent: region.continent,
            options: region.options,
        })
        .collect();

    Ok(success_response(converted_regions))
}

#[instrument(skip(state))]
pub async fn list_os(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrOS>>>> {
    let os_list = state
        .vultr_client
        .list_os()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    // Convert to our model format
    let converted_os: Vec<VultrOS> = os_list
        .into_iter()
        .map(|os| VultrOS {
            id: os.id,
            name: os.name,
            arch: os.arch,
            family: os.family,
        })
        .collect();

    Ok(success_response(converted_os))
}

#[instrument(skip(state))]
pub async fn list_ssh_keys(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrSSHKey>>>> {
    let ssh_keys = state
        .vultr_client
        .list_ssh_keys()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(ssh_keys))
}

#[instrument(skip(state))]
pub async fn list_backups(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrBackup>>>> {
    let backups = state
        .vultr_client
        .list_backups()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(backups))
}

// Bare Metal endpoints
#[instrument(skip(state))]
pub async fn list_bare_metal(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrBareMetal>>>> {
    let bare_metals = state
        .vultr_client
        .list_bare_metal()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bare_metals))
}

#[instrument(skip(state, request))]
pub async fn create_bare_metal(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateBareMetalRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBareMetal>>> {
    let bare_metal = state
        .vultr_client
        .create_bare_metal(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bare_metal))
}

#[instrument(skip(state))]
pub async fn get_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBareMetal>>> {
    let bare_metal = state
        .vultr_client
        .get_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bare_metal))
}

#[instrument(skip(state, request))]
pub async fn update_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
    Json(request): Json<UpdateBareMetalRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBareMetal>>> {
    let bare_metal = state
        .vultr_client
        .update_bare_metal(&baremetal_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bare_metal))
}

#[instrument(skip(state))]
pub async fn delete_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_ipv4(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<BareMetalIpv4Info>>>> {
    let ipv4_info = state
        .vultr_client
        .get_bare_metal_ipv4(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(ipv4_info))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_ipv6(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<BareMetalIpv6Info>>>> {
    let ipv6_info = state
        .vultr_client
        .get_bare_metal_ipv6(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(ipv6_info))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_bandwidth(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<BareMetalBandwidth>>> {
    let bandwidth = state
        .vultr_client
        .get_bare_metal_bandwidth(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bandwidth))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_user_data(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<BareMetalUserData>>> {
    let user_data = state
        .vultr_client
        .get_bare_metal_user_data(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(user_data))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_upgrades(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<BareMetalUpgrades>>> {
    let upgrades = state
        .vultr_client
        .get_bare_metal_upgrades(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(upgrades))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_vnc(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<BareMetalVncInfo>>> {
    let vnc = state
        .vultr_client
        .get_bare_metal_vnc(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(vnc))
}

#[instrument(skip(state))]
pub async fn list_bare_metal_vpcs(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<BareMetalVpcInfo>>>> {
    let vpcs = state
        .vultr_client
        .list_bare_metal_vpcs(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(vpcs))
}

#[instrument(skip(state))]
pub async fn attach_bare_metal_vpc(
    State(state): State<Arc<AppState>>,
    Path((baremetal_id, vpc_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .attach_bare_metal_vpc(&baremetal_id, &vpc_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn detach_bare_metal_vpc(
    State(state): State<Arc<AppState>>,
    Path((baremetal_id, vpc_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .detach_bare_metal_vpc(&baremetal_id, &vpc_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn start_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .start_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn reboot_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .reboot_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn reinstall_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBareMetal>>> {
    let bare_metal = state
        .vultr_client
        .reinstall_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bare_metal))
}

#[instrument(skip(state))]
pub async fn halt_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .halt_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, instance_ids))]
pub async fn halt_bare_metals(
    State(state): State<Arc<AppState>>,
    Json(instance_ids): Json<Vec<String>>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .halt_bare_metals(instance_ids)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, instance_ids))]
pub async fn reboot_bare_metals(
    State(state): State<Arc<AppState>>,
    Json(instance_ids): Json<Vec<String>>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .reboot_bare_metals(instance_ids)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, instance_ids))]
pub async fn start_bare_metals(
    State(state): State<Arc<AppState>>,
    Json(instance_ids): Json<Vec<String>>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .start_bare_metals(instance_ids)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

// Block Storage endpoints
#[instrument(skip(state))]
pub async fn list_block_storage(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrBlockStorage>>>> {
    let blocks = state
        .vultr_client
        .list_block_storage()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(blocks))
}

#[instrument(skip(state, request))]
pub async fn create_block_storage(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateBlockStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBlockStorage>>> {
    let block = state
        .vultr_client
        .create_block_storage(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(block))
}

#[instrument(skip(state))]
pub async fn get_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBlockStorage>>> {
    let block = state
        .vultr_client
        .get_block_storage(&block_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(block))
}

#[instrument(skip(state, request))]
pub async fn update_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
    Json(request): Json<UpdateBlockStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBlockStorage>>> {
    let block = state
        .vultr_client
        .update_block_storage(&block_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(block))
}

#[instrument(skip(state))]
pub async fn delete_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_block_storage(&block_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn attach_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
    Json(request): Json<AttachBlockStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .attach_block_storage(&block_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn detach_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
    Json(request): Json<DetachBlockStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .detach_block_storage(&block_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

// Additional Billing endpoints
#[instrument(skip(state))]
pub async fn get_invoice_items(
    State(state): State<Arc<AppState>>,
    Path(invoice_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrInvoiceItem>>>> {
    let invoice_items = state
        .vultr_client
        .get_invoice_items(&invoice_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(invoice_items))
}

#[instrument(skip(state))]
pub async fn get_pending_charges(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPendingCharges>>> {
    let pending_charges = state
        .vultr_client
        .get_pending_charges()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(pending_charges))
}

// CDN Pull Zone endpoints
#[instrument(skip(state))]
pub async fn list_pull_zones(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrPullZone>>>> {
    let pull_zones = state
        .vultr_client
        .list_pull_zones()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(pull_zones))
}

#[instrument(skip(state, request))]
pub async fn create_pull_zone(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreatePullZoneRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPullZone>>> {
    let pull_zone = state
        .vultr_client
        .create_pull_zone(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(pull_zone))
}

#[instrument(skip(state))]
pub async fn get_pull_zone(
    State(state): State<Arc<AppState>>,
    Path(pullzone_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPullZone>>> {
    let pull_zone = state
        .vultr_client
        .get_pull_zone(&pullzone_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(pull_zone))
}

#[instrument(skip(state, request))]
pub async fn update_pull_zone(
    State(state): State<Arc<AppState>>,
    Path(pullzone_id): Path<String>,
    Json(request): Json<UpdatePullZoneRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPullZone>>> {
    let pull_zone = state
        .vultr_client
        .update_pull_zone(&pullzone_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(pull_zone))
}

#[instrument(skip(state))]
pub async fn delete_pull_zone(
    State(state): State<Arc<AppState>>,
    Path(pullzone_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_pull_zone(&pullzone_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn purge_pull_zone(
    State(state): State<Arc<AppState>>,
    Path(pullzone_id): Path<String>,
    Json(request): Json<PurgePullZoneRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .purge_pull_zone(&pullzone_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

// CDN Push Zone endpoints
#[instrument(skip(state))]
pub async fn list_push_zones(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrPushZone>>>> {
    let push_zones = state
        .vultr_client
        .list_push_zones()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(push_zones))
}

#[instrument(skip(state, request))]
pub async fn create_push_zone(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreatePushZoneRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPushZone>>> {
    let push_zone = state
        .vultr_client
        .create_push_zone(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(push_zone))
}

#[instrument(skip(state))]
pub async fn get_push_zone(
    State(state): State<Arc<AppState>>,
    Path(pushzone_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPushZone>>> {
    let push_zone = state
        .vultr_client
        .get_push_zone(&pushzone_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(push_zone))
}

#[instrument(skip(state, request))]
pub async fn update_push_zone(
    State(state): State<Arc<AppState>>,
    Path(pushzone_id): Path<String>,
    Json(request): Json<UpdatePushZoneRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPushZone>>> {
    let push_zone = state
        .vultr_client
        .update_push_zone(&pushzone_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(push_zone))
}

#[instrument(skip(state))]
pub async fn delete_push_zone(
    State(state): State<Arc<AppState>>,
    Path(pushzone_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_push_zone(&pushzone_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_push_zone_files(
    State(state): State<Arc<AppState>>,
    Path(pushzone_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrPushZoneFile>>>> {
    let files = state
        .vultr_client
        .get_push_zone_files(&pushzone_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(files))
}

#[instrument(skip(state))]
pub async fn delete_push_zone_file(
    State(state): State<Arc<AppState>>,
    Path((pushzone_id, file_name)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_push_zone_file(&pushzone_id, &file_name)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

// Enhanced Container Registry endpoints
#[instrument(skip(state, request))]
pub async fn create_registry(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateRegistryRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrContainerRegistry>>> {
    let registry = state
        .vultr_client
        .create_registry(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(registry))
}

#[instrument(skip(state, request))]
pub async fn update_registry(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
    Json(request): Json<UpdateRegistryRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrContainerRegistry>>> {
    let registry = state
        .vultr_client
        .update_registry(&registry_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(registry))
}

#[instrument(skip(state))]
pub async fn delete_registry(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_registry(&registry_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_registry_replications(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegistryReplication>>>> {
    let replications = state
        .vultr_client
        .list_registry_replications(&registry_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(replications))
}

#[instrument(skip(state, request))]
pub async fn create_registry_replication(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
    Json(request): Json<CreateReplicationRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryReplication>>> {
    let replication = state
        .vultr_client
        .create_registry_replication(&registry_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(replication))
}

#[instrument(skip(state))]
pub async fn get_registry_replication(
    State(state): State<Arc<AppState>>,
    Path((registry_id, replication_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryReplication>>> {
    let replication = state
        .vultr_client
        .get_registry_replication(&registry_id, &replication_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(replication))
}

#[instrument(skip(state))]
pub async fn delete_registry_replication(
    State(state): State<Arc<AppState>>,
    Path((registry_id, replication_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_registry_replication(&registry_id, &replication_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_registry_repositories(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegistryRepository>>>> {
    let repositories = state
        .vultr_client
        .list_registry_repositories(&registry_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(repositories))
}

#[instrument(skip(state))]
pub async fn get_registry_repository(
    State(state): State<Arc<AppState>>,
    Path((registry_id, repository_image)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryRepository>>> {
    let repository = state
        .vultr_client
        .get_registry_repository(&registry_id, &repository_image)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(repository))
}

#[instrument(skip(state, request))]
pub async fn update_registry_repository(
    State(state): State<Arc<AppState>>,
    Path((registry_id, repository_image)): Path<(String, String)>,
    Json(request): Json<UpdateRepositoryRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryRepository>>> {
    let repository = state
        .vultr_client
        .update_registry_repository(&registry_id, &repository_image, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(repository))
}

#[instrument(skip(state))]
pub async fn delete_registry_repository(
    State(state): State<Arc<AppState>>,
    Path((registry_id, repository_image)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_registry_repository(&registry_id, &repository_image)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn create_registry_docker_credentials(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDockerCredentials>>> {
    let credentials = state
        .vultr_client
        .create_registry_docker_credentials(&registry_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(credentials))
}

#[instrument(skip(state))]
pub async fn create_registry_kubernetes_docker_credentials(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrKubernetesDockerCredentials>>> {
    let credentials = state
        .vultr_client
        .create_registry_kubernetes_docker_credentials(&registry_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(credentials))
}

#[instrument(skip(state, request))]
pub async fn update_registry_password(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
    Json(request): Json<UpdateRegistryPasswordRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_registry_password(&registry_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_registry_robots(
    State(state): State<Arc<AppState>>,
    Path(registry_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegistryRobot>>>> {
    let robots = state
        .vultr_client
        .list_registry_robots(&registry_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(robots))
}

#[instrument(skip(state))]
pub async fn get_registry_robot(
    State(state): State<Arc<AppState>>,
    Path((registry_id, robot_name)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryRobot>>> {
    let robot = state
        .vultr_client
        .get_registry_robot(&registry_id, &robot_name)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(robot))
}

#[instrument(skip(state, request))]
pub async fn update_registry_robot(
    State(state): State<Arc<AppState>>,
    Path((registry_id, robot_name)): Path<(String, String)>,
    Json(request): Json<UpdateRobotRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryRobot>>> {
    let robot = state
        .vultr_client
        .update_registry_robot(&registry_id, &robot_name, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(robot))
}

#[instrument(skip(state))]
pub async fn delete_registry_robot(
    State(state): State<Arc<AppState>>,
    Path((registry_id, robot_name)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_registry_robot(&registry_id, &robot_name)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_registry_repository_artifacts(
    State(state): State<Arc<AppState>>,
    Path((registry_id, repository_image)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegistryArtifact>>>> {
    let artifacts = state
        .vultr_client
        .list_registry_repository_artifacts(&registry_id, &repository_image)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(artifacts))
}

#[instrument(skip(state))]
pub async fn get_registry_repository_artifact(
    State(state): State<Arc<AppState>>,
    Path((registry_id, repository_image, artifact_digest)): Path<(String, String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrRegistryArtifact>>> {
    let artifact = state
        .vultr_client
        .get_registry_repository_artifact(&registry_id, &repository_image, &artifact_digest)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(artifact))
}

#[instrument(skip(state))]
pub async fn delete_registry_repository_artifact(
    State(state): State<Arc<AppState>>,
    Path((registry_id, repository_image, artifact_digest)): Path<(String, String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_registry_repository_artifact(&registry_id, &repository_image, &artifact_digest)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_registry_regions(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegistryRegion>>>> {
    let regions = state
        .vultr_client
        .list_registry_regions()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(regions))
}

#[instrument(skip(state))]
pub async fn list_registry_plans(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegistryPlan>>>> {
    let plans = state
        .vultr_client
        .list_registry_plans()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(plans))
}

// Enhanced DNS endpoints
#[instrument(skip(state))]
pub async fn list_dns_domains(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrDNSDomainDetailed>>>> {
    let domains = state
        .vultr_client
        .list_dns_domains()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(domains))
}

#[instrument(skip(state, request))]
pub async fn create_dns_domain(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateDNSDomainRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDNSDomainDetailed>>> {
    let domain = state
        .vultr_client
        .create_dns_domain(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(domain))
}

#[instrument(skip(state))]
pub async fn get_dns_domain(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDNSDomainDetailed>>> {
    let domain_info = state
        .vultr_client
        .get_dns_domain(&domain)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(domain_info))
}

#[instrument(skip(state))]
pub async fn delete_dns_domain(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_dns_domain(&domain)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn update_dns_domain(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
    Json(request): Json<UpdateDNSDomainRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_dns_domain(&domain, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_dns_domain_soa(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDNSSOA>>> {
    let soa = state
        .vultr_client
        .get_dns_domain_soa(&domain)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(soa))
}

#[instrument(skip(state, request))]
pub async fn update_dns_domain_soa(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
    Json(request): Json<UpdateDNSSOARequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_dns_domain_soa(&domain, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_dns_domain_dnssec(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<String>>>> {
    let dnssec = state
        .vultr_client
        .get_dns_domain_dnssec(&domain)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(dnssec))
}

#[instrument(skip(state))]
pub async fn list_dns_domain_records(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrDNSRecordDetailed>>>> {
    let records = state
        .vultr_client
        .list_dns_domain_records(&domain)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(records))
}

#[instrument(skip(state, request))]
pub async fn create_dns_domain_record(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
    Json(request): Json<CreateDNSRecordRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDNSRecordDetailed>>> {
    let record = state
        .vultr_client
        .create_dns_domain_record(&domain, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(record))
}

#[instrument(skip(state))]
pub async fn get_dns_domain_record(
    State(state): State<Arc<AppState>>,
    Path((domain, record_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrDNSRecordDetailed>>> {
    let record = state
        .vultr_client
        .get_dns_domain_record(&domain, &record_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(record))
}

#[instrument(skip(state, request))]
pub async fn update_dns_domain_record(
    State(state): State<Arc<AppState>>,
    Path((domain, record_id)): Path<(String, String)>,
    Json(request): Json<UpdateDNSRecordRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_dns_domain_record(&domain, &record_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_dns_domain_record(
    State(state): State<Arc<AppState>>,
    Path((domain, record_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_dns_domain_record(&domain, &record_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

// Enhanced Firewall endpoints
#[instrument(skip(state))]
pub async fn list_firewall_groups(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrFirewallGroupDetailed>>>> {
    let firewall_groups = state
        .vultr_client
        .list_firewall_groups()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(firewall_groups))
}

#[instrument(skip(state, request))]
pub async fn create_firewall_group(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateFirewallGroupRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrFirewallGroupDetailed>>> {
    let firewall_group = state
        .vultr_client
        .create_firewall_group(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(firewall_group))
}

#[instrument(skip(state))]
pub async fn get_firewall_group(
    State(state): State<Arc<AppState>>,
    Path(firewall_group_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrFirewallGroupDetailed>>> {
    let firewall_group = state
        .vultr_client
        .get_firewall_group(&firewall_group_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(firewall_group))
}

#[instrument(skip(state, request))]
pub async fn update_firewall_group(
    State(state): State<Arc<AppState>>,
    Path(firewall_group_id): Path<String>,
    Json(request): Json<UpdateFirewallGroupRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .update_firewall_group(&firewall_group_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn delete_firewall_group(
    State(state): State<Arc<AppState>>,
    Path(firewall_group_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_firewall_group(&firewall_group_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn list_firewall_group_rules(
    State(state): State<Arc<AppState>>,
    Path(firewall_group_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrFirewallRuleDetailed>>>> {
    let firewall_rules = state
        .vultr_client
        .list_firewall_group_rules(&firewall_group_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(firewall_rules))
}

#[instrument(skip(state, request))]
pub async fn create_firewall_group_rule(
    State(state): State<Arc<AppState>>,
    Path(firewall_group_id): Path<String>,
    Json(request): Json<CreateFirewallRuleRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrFirewallRuleDetailed>>> {
    let firewall_rule = state
        .vultr_client
        .create_firewall_group_rule(&firewall_group_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(firewall_rule))
}

#[instrument(skip(state))]
pub async fn get_firewall_group_rule(
    State(state): State<Arc<AppState>>,
    Path((firewall_group_id, firewall_rule_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrFirewallRuleDetailed>>> {
    let firewall_rule = state
        .vultr_client
        .get_firewall_group_rule(&firewall_group_id, &firewall_rule_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(firewall_rule))
}

#[instrument(skip(state))]
pub async fn delete_firewall_group_rule(
    State(state): State<Arc<AppState>>,
    Path((firewall_group_id, firewall_rule_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_firewall_group_rule(&firewall_group_id, &firewall_rule_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}
