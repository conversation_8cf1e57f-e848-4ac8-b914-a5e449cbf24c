use crate::{
    controllers::{success_response, ControllerResult},
    models::{
        BareMetalBandwidth, BareMetalIpv4Info, BareMetalIpv6Info, BareMetalUpgrades,
        BareMetalUserData, BareMetalVncInfo, BareMetalVpcInfo, CreateBareMetalRequest,
        UpdateBareMetalRequest, VultrOS, VultrPlan, VultrRegion, VultrBareMetal,
    },
    vultr::models::{
        VultrSSHKey, VultrAccount, VultrAccountBGP, VultrAccountBandwidth,
        VultrBlockStorage, CreateBlockStorageRequest, UpdateBlockStorageRequest,
        AttachBlockStorageRequest, DetachBlockStorageRequest, VultrInvoiceItem,
        VultrPendingCharges,
    },
    AppState,
};
use axum::{
    extract::{Path, State},
    Json,
};
use std::sync::Arc;
use tracing::instrument;

// Account endpoints
#[instrument(skip(state))]
pub async fn get_account(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrAccount>>> {
    let account = state
        .vultr_client
        .get_account()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(account))
}

#[instrument(skip(state))]
pub async fn get_account_bgp(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrAccountBGP>>> {
    let bgp_info = state
        .vultr_client
        .get_account_bgp()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bgp_info))
}

#[instrument(skip(state))]
pub async fn get_account_bandwidth(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrAccountBandwidth>>> {
    let bandwidth_info = state
        .vultr_client
        .get_account_bandwidth()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bandwidth_info))
}

#[instrument(skip(state))]
pub async fn list_plans(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrPlan>>>> {
    let plans = state
        .vultr_client
        .list_plans()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    // Convert to our model format
    let converted_plans: Vec<VultrPlan> = plans
        .into_iter()
        .map(|plan| VultrPlan {
            id: plan.id,
            vcpu_count: plan.vcpu_count,
            ram: plan.ram,
            disk: plan.disk,
            bandwidth: plan.bandwidth,
            monthly_cost: plan.monthly_cost,
            type_: plan.plan_type,
            locations: plan.locations,
        })
        .collect();

    Ok(success_response(converted_plans))
}

#[instrument(skip(state))]
pub async fn list_regions(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrRegion>>>> {
    let regions = state
        .vultr_client
        .list_regions()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    // Convert to our model format
    let converted_regions: Vec<VultrRegion> = regions
        .into_iter()
        .map(|region| VultrRegion {
            id: region.id,
            city: region.city,
            country: region.country,
            continent: region.continent,
            options: region.options,
        })
        .collect();

    Ok(success_response(converted_regions))
}

#[instrument(skip(state))]
pub async fn list_os(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrOS>>>> {
    let os_list = state
        .vultr_client
        .list_os()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    // Convert to our model format
    let converted_os: Vec<VultrOS> = os_list
        .into_iter()
        .map(|os| VultrOS {
            id: os.id,
            name: os.name,
            arch: os.arch,
            family: os.family,
        })
        .collect();

    Ok(success_response(converted_os))
}

#[instrument(skip(state))]
pub async fn list_ssh_keys(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrSSHKey>>>> {
    let ssh_keys = state
        .vultr_client
        .list_ssh_keys()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(ssh_keys))
}

#[instrument(skip(state))]
pub async fn list_backups(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrBackup>>>> {
    let backups = state
        .vultr_client
        .list_backups()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(backups))
}

// Bare Metal endpoints
#[instrument(skip(state))]
pub async fn list_bare_metal(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrBareMetal>>>> {
    let bare_metals = state
        .vultr_client
        .list_bare_metal()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bare_metals))
}

#[instrument(skip(state, request))]
pub async fn create_bare_metal(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateBareMetalRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBareMetal>>> {
    let bare_metal = state
        .vultr_client
        .create_bare_metal(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bare_metal))
}

#[instrument(skip(state))]
pub async fn get_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBareMetal>>> {
    let bare_metal = state
        .vultr_client
        .get_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bare_metal))
}

#[instrument(skip(state, request))]
pub async fn update_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
    Json(request): Json<UpdateBareMetalRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBareMetal>>> {
    let bare_metal = state
        .vultr_client
        .update_bare_metal(&baremetal_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bare_metal))
}

#[instrument(skip(state))]
pub async fn delete_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_ipv4(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<BareMetalIpv4Info>>>> {
    let ipv4_info = state
        .vultr_client
        .get_bare_metal_ipv4(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(ipv4_info))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_ipv6(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<BareMetalIpv6Info>>>> {
    let ipv6_info = state
        .vultr_client
        .get_bare_metal_ipv6(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(ipv6_info))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_bandwidth(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<BareMetalBandwidth>>> {
    let bandwidth = state
        .vultr_client
        .get_bare_metal_bandwidth(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bandwidth))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_user_data(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<BareMetalUserData>>> {
    let user_data = state
        .vultr_client
        .get_bare_metal_user_data(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(user_data))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_upgrades(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<BareMetalUpgrades>>> {
    let upgrades = state
        .vultr_client
        .get_bare_metal_upgrades(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(upgrades))
}

#[instrument(skip(state))]
pub async fn get_bare_metal_vnc(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<BareMetalVncInfo>>> {
    let vnc = state
        .vultr_client
        .get_bare_metal_vnc(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(vnc))
}

#[instrument(skip(state))]
pub async fn list_bare_metal_vpcs(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<BareMetalVpcInfo>>>> {
    let vpcs = state
        .vultr_client
        .list_bare_metal_vpcs(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(vpcs))
}

#[instrument(skip(state))]
pub async fn attach_bare_metal_vpc(
    State(state): State<Arc<AppState>>,
    Path((baremetal_id, vpc_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .attach_bare_metal_vpc(&baremetal_id, &vpc_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn detach_bare_metal_vpc(
    State(state): State<Arc<AppState>>,
    Path((baremetal_id, vpc_id)): Path<(String, String)>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .detach_bare_metal_vpc(&baremetal_id, &vpc_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn start_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .start_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn reboot_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .reboot_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state))]
pub async fn reinstall_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBareMetal>>> {
    let bare_metal = state
        .vultr_client
        .reinstall_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(bare_metal))
}

#[instrument(skip(state))]
pub async fn halt_bare_metal(
    State(state): State<Arc<AppState>>,
    Path(baremetal_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .halt_bare_metal(&baremetal_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, instance_ids))]
pub async fn halt_bare_metals(
    State(state): State<Arc<AppState>>,
    Json(instance_ids): Json<Vec<String>>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .halt_bare_metals(instance_ids)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, instance_ids))]
pub async fn reboot_bare_metals(
    State(state): State<Arc<AppState>>,
    Json(instance_ids): Json<Vec<String>>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .reboot_bare_metals(instance_ids)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, instance_ids))]
pub async fn start_bare_metals(
    State(state): State<Arc<AppState>>,
    Json(instance_ids): Json<Vec<String>>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .start_bare_metals(instance_ids)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

// Block Storage endpoints
#[instrument(skip(state))]
pub async fn list_block_storage(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrBlockStorage>>>> {
    let blocks = state
        .vultr_client
        .list_block_storage()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(blocks))
}

#[instrument(skip(state, request))]
pub async fn create_block_storage(
    State(state): State<Arc<AppState>>,
    Json(request): Json<CreateBlockStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBlockStorage>>> {
    let block = state
        .vultr_client
        .create_block_storage(request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(block))
}

#[instrument(skip(state))]
pub async fn get_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBlockStorage>>> {
    let block = state
        .vultr_client
        .get_block_storage(&block_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(block))
}

#[instrument(skip(state, request))]
pub async fn update_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
    Json(request): Json<UpdateBlockStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrBlockStorage>>> {
    let block = state
        .vultr_client
        .update_block_storage(&block_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(block))
}

#[instrument(skip(state))]
pub async fn delete_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .delete_block_storage(&block_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn attach_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
    Json(request): Json<AttachBlockStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .attach_block_storage(&block_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

#[instrument(skip(state, request))]
pub async fn detach_block_storage(
    State(state): State<Arc<AppState>>,
    Path(block_id): Path<String>,
    Json(request): Json<DetachBlockStorageRequest>,
) -> ControllerResult<Json<crate::models::ApiResponse<()>>> {
    state
        .vultr_client
        .detach_block_storage(&block_id, request)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(()))
}

// Additional Billing endpoints
#[instrument(skip(state))]
pub async fn get_invoice_items(
    State(state): State<Arc<AppState>>,
    Path(invoice_id): Path<String>,
) -> ControllerResult<Json<crate::models::ApiResponse<Vec<VultrInvoiceItem>>>> {
    let invoice_items = state
        .vultr_client
        .get_invoice_items(&invoice_id)
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(invoice_items))
}

#[instrument(skip(state))]
pub async fn get_pending_charges(
    State(state): State<Arc<AppState>>,
) -> ControllerResult<Json<crate::models::ApiResponse<VultrPendingCharges>>> {
    let pending_charges = state
        .vultr_client
        .get_pending_charges()
        .await
        .map_err(|e| crate::controllers::ControllerError::ExternalApi(format!("Vultr API error: {}", e)))?;

    Ok(success_response(pending_charges))
}
