use mongodb::{
    options::{ClientOptions, ResolverConfig},
    Client, Collection, Database as MongoDatabase,
};
use anyhow::Result;
use tracing::{info, instrument};

#[derive(Clone)]
pub struct Database {
    db: MongoDatabase,
}

impl Database {
    #[instrument]
    pub async fn new(database_url: &str) -> Result<Self> {
        let client_options = ClientOptions::parse_with_resolver_config(
            database_url,
            ResolverConfig::cloudflare(),
        )
        .await?;

        let client = Client::with_options(client_options)?;
        
        // Test the connection
        client
            .database("admin")
            .run_command(mongodb::bson::doc! {"ping": 1}, None)
            .await?;

        let db = client.database("achidas");
        
        info!("Connected to MongoDB database: achidas");
        
        Ok(Self { db })
    }

    pub fn collection<T>(&self, name: &str) -> Collection<T> {
        self.db.collection(name)
    }

    pub fn database(&self) -> &MongoDatabase {
        &self.db
    }

    #[instrument(skip(self))]
    pub async fn create_indexes(&self) -> Result<()> {
        use mongodb::{bson::doc, options::IndexOptions, IndexModel};

        // User indexes
        let users_collection: Collection<crate::models::User> = self.collection("users");
        let email_index = IndexModel::builder()
            .keys(doc! { "email": 1 })
            .options(IndexOptions::builder().unique(true).build())
            .build();
        users_collection.create_index(email_index, None).await?;

        // Instance indexes
        let instances_collection: Collection<crate::models::Instance> = self.collection("instances");
        let user_id_index = IndexModel::builder()
            .keys(doc! { "user_id": 1 })
            .build();
        let vultr_id_index = IndexModel::builder()
            .keys(doc! { "vultr_instance_id": 1 })
            .options(IndexOptions::builder().unique(true).build())
            .build();
        instances_collection.create_index(user_id_index, None).await?;
        instances_collection.create_index(vultr_id_index, None).await?;

        // Billing indexes
        let billing_collection: Collection<crate::models::BillingAccount> = self.collection("billing_accounts");
        let billing_user_index = IndexModel::builder()
            .keys(doc! { "user_id": 1 })
            .options(IndexOptions::builder().unique(true).build())
            .build();
        billing_collection.create_index(billing_user_index, None).await?;

        // Invoice indexes
        let invoices_collection: Collection<crate::models::Invoice> = self.collection("invoices");
        let invoice_user_index = IndexModel::builder()
            .keys(doc! { "user_id": 1 })
            .build();
        let invoice_number_index = IndexModel::builder()
            .keys(doc! { "invoice_number": 1 })
            .options(IndexOptions::builder().unique(true).build())
            .build();
        invoices_collection.create_index(invoice_user_index, None).await?;
        invoices_collection.create_index(invoice_number_index, None).await?;

        // Usage records indexes
        let usage_collection: Collection<crate::models::UsageRecord> = self.collection("usage_records");
        let usage_user_index = IndexModel::builder()
            .keys(doc! { "user_id": 1, "billing_period": 1 })
            .build();
        let usage_instance_index = IndexModel::builder()
            .keys(doc! { "instance_id": 1, "recorded_at": 1 })
            .build();
        usage_collection.create_index(usage_user_index, None).await?;
        usage_collection.create_index(usage_instance_index, None).await?;

        info!("Database indexes created successfully");
        Ok(())
    }
}
