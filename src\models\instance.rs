use bson::oid::ObjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Instance {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub vultr_instance_id: String,
    pub name: String,
    pub region: String,
    pub plan: String,
    pub os: String,
    pub status: InstanceStatus,
    pub ip_address: Option<String>,
    pub internal_ip: Option<String>,
    pub vcpu_count: u32,
    pub ram: u32, // MB
    pub disk: u32, // GB
    pub bandwidth: u32, // GB
    pub monthly_cost: f64,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub tags: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InstanceStatus {
    Pending,
    Installing,
    Running,
    Stopped,
    Suspended,
    Destroyed,
    Error,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct CreateInstanceRequest {
    #[validate(length(min = 1, max = 255))]
    pub name: String,
    #[validate(length(min = 1))]
    pub region: String,
    #[validate(length(min = 1))]
    pub plan: String,
    #[validate(length(min = 1))]
    pub os: String,
    pub ssh_key_ids: Option<Vec<String>>,
    pub startup_script_id: Option<String>,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstanceResponse {
    pub id: String,
    pub vultr_instance_id: String,
    pub name: String,
    pub region: String,
    pub plan: String,
    pub os: String,
    pub status: InstanceStatus,
    pub ip_address: Option<String>,
    pub internal_ip: Option<String>,
    pub vcpu_count: u32,
    pub ram: u32,
    pub disk: u32,
    pub bandwidth: u32,
    pub monthly_cost: f64,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub tags: Vec<String>,
}

impl From<Instance> for InstanceResponse {
    fn from(instance: Instance) -> Self {
        Self {
            id: instance.id.map(|id| id.to_hex()).unwrap_or_default(),
            vultr_instance_id: instance.vultr_instance_id,
            name: instance.name,
            region: instance.region,
            plan: instance.plan,
            os: instance.os,
            status: instance.status,
            ip_address: instance.ip_address,
            internal_ip: instance.internal_ip,
            vcpu_count: instance.vcpu_count,
            ram: instance.ram,
            disk: instance.disk,
            bandwidth: instance.bandwidth,
            monthly_cost: instance.monthly_cost,
            created_at: instance.created_at,
            updated_at: instance.updated_at,
            tags: instance.tags,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPlan {
    pub id: String,
    pub vcpu_count: u32,
    pub ram: u32,
    pub disk: u32,
    pub bandwidth: u32,
    pub monthly_cost: f64,
    pub type_: String,
    pub locations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegion {
    pub id: String,
    pub city: String,
    pub country: String,
    pub continent: String,
    pub options: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrOS {
    pub id: String,
    pub name: String,
    pub arch: String,
    pub family: String,
}
