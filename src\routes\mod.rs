use crate::{controllers, middleware::auth::auth_middleware, AppState};
use axum::{
    middleware,
    routing::{delete, get, post, put},
    Router,
};
use std::sync::Arc;
use tower::ServiceBuilder;
use tower_http::trace::TraceLayer;

pub fn create_router(state: Arc<AppState>) -> Router {
    Router::new()
        // Health check
        .route("/health", get(controllers::health::health_check))
        
        // Authentication routes (public)
        .route("/auth/register", post(controllers::auth::register))
        .route("/auth/login", post(controllers::auth::login))
        .route("/auth/refresh", post(controllers::auth::refresh_token))
        
        // Protected routes
        .nest("/api/v1", protected_routes())
        
        // Add state and middleware
        .with_state(state)
        .layer(ServiceBuilder::new().layer(TraceLayer::new_for_http()))
}

fn protected_routes() -> Router<Arc<AppState>> {
    Router::new()
        // User management
        .route("/users/profile", get(controllers::users::get_profile))
        .route("/users/profile", put(controllers::users::update_profile))
        
        // Instance management
        .route("/instances", get(controllers::instances::list_instances))
        .route("/instances", post(controllers::instances::create_instance))
        .route("/instances/:id", get(controllers::instances::get_instance))
        .route("/instances/:id", put(controllers::instances::update_instance))
        .route("/instances/:id", delete(controllers::instances::delete_instance))
        .route("/instances/:id/start", post(controllers::instances::start_instance))
        .route("/instances/:id/stop", post(controllers::instances::stop_instance))
        .route("/instances/:id/restart", post(controllers::instances::restart_instance))
        
        // Vultr resources (read-only)
        .route("/vultr/account", get(controllers::vultr::get_account))
        .route("/vultr/account/bgp", get(controllers::vultr::get_account_bgp))
        .route("/vultr/account/bandwidth", get(controllers::vultr::get_account_bandwidth))
        .route("/vultr/plans", get(controllers::vultr::list_plans))
        .route("/vultr/regions", get(controllers::vultr::list_regions))
        .route("/vultr/os", get(controllers::vultr::list_os))
        .route("/vultr/ssh-keys", get(controllers::vultr::list_ssh_keys))
        .route("/vultr/backups", get(controllers::vultr::list_backups))
        
        // Bare Metal routes
        .route("/vultr/bare-metal", get(controllers::vultr::list_bare_metal))
        .route("/vultr/bare-metal", post(controllers::vultr::create_bare_metal))
        .route("/vultr/bare-metal/:id", get(controllers::vultr::get_bare_metal))
        .route("/vultr/bare-metal/:id", put(controllers::vultr::update_bare_metal))
        .route("/vultr/bare-metal/:id", delete(controllers::vultr::delete_bare_metal))
        .route("/vultr/bare-metal/:id/ipv4", get(controllers::vultr::get_bare_metal_ipv4))
        .route("/vultr/bare-metal/:id/ipv6", get(controllers::vultr::get_bare_metal_ipv6))
        .route("/vultr/bare-metal/:id/bandwidth", get(controllers::vultr::get_bare_metal_bandwidth))
        .route("/vultr/bare-metal/:id/user-data", get(controllers::vultr::get_bare_metal_user_data))
        .route("/vultr/bare-metal/:id/upgrades", get(controllers::vultr::get_bare_metal_upgrades))
        .route("/vultr/bare-metal/:id/vnc", get(controllers::vultr::get_bare_metal_vnc))
        .route("/vultr/bare-metal/:id/start", post(controllers::vultr::start_bare_metal))
        .route("/vultr/bare-metal/:id/reboot", post(controllers::vultr::reboot_bare_metal))
        .route("/vultr/bare-metal/:id/reinstall", post(controllers::vultr::reinstall_bare_metal))
        .route("/vultr/bare-metal/:id/halt", post(controllers::vultr::halt_bare_metal))
        .route("/vultr/bare-metal/halt", post(controllers::vultr::halt_bare_metals))
        .route("/vultr/bare-metal/reboot", post(controllers::vultr::reboot_bare_metals))
        .route("/vultr/bare-metal/start", post(controllers::vultr::start_bare_metals))
        .route("/vultr/bare-metal/:id/vpcs", get(controllers::vultr::list_bare_metal_vpcs))
        .route("/vultr/bare-metal/:id/vpcs/:vpc_id", post(controllers::vultr::attach_bare_metal_vpc))
        .route("/vultr/bare-metal/:id/vpcs/:vpc_id", delete(controllers::vultr::detach_bare_metal_vpc))
        
        // Block Storage routes
        .route("/vultr/blocks", get(controllers::vultr::list_block_storage))
        .route("/vultr/blocks", post(controllers::vultr::create_block_storage))
        .route("/vultr/blocks/:id", get(controllers::vultr::get_block_storage))
        .route("/vultr/blocks/:id", put(controllers::vultr::update_block_storage))
        .route("/vultr/blocks/:id", delete(controllers::vultr::delete_block_storage))
        .route("/vultr/blocks/:id/attach", post(controllers::vultr::attach_block_storage))
        .route("/vultr/blocks/:id/detach", post(controllers::vultr::detach_block_storage))

        // Additional Vultr Billing routes
        .route("/vultr/billing/invoice-items/:invoice_id", get(controllers::vultr::get_invoice_items))
        .route("/vultr/billing/pending-charges", get(controllers::vultr::get_pending_charges))

        // CDN Pull Zone routes
        .route("/vultr/cdn/pull-zones", get(controllers::vultr::list_pull_zones))
        .route("/vultr/cdn/pull-zones", post(controllers::vultr::create_pull_zone))
        .route("/vultr/cdn/pull-zones/:id", get(controllers::vultr::get_pull_zone))
        .route("/vultr/cdn/pull-zones/:id", put(controllers::vultr::update_pull_zone))
        .route("/vultr/cdn/pull-zones/:id", delete(controllers::vultr::delete_pull_zone))
        .route("/vultr/cdn/pull-zones/:id/purge", post(controllers::vultr::purge_pull_zone))

        // CDN Push Zone routes
        .route("/vultr/cdn/push-zones", get(controllers::vultr::list_push_zones))
        .route("/vultr/cdn/push-zones", post(controllers::vultr::create_push_zone))
        .route("/vultr/cdn/push-zones/:id", get(controllers::vultr::get_push_zone))
        .route("/vultr/cdn/push-zones/:id", put(controllers::vultr::update_push_zone))
        .route("/vultr/cdn/push-zones/:id", delete(controllers::vultr::delete_push_zone))
        .route("/vultr/cdn/push-zones/:id/files", get(controllers::vultr::get_push_zone_files))
        .route("/vultr/cdn/push-zones/:id/files/:file_name", delete(controllers::vultr::delete_push_zone_file))

        // Enhanced Container Registry routes
        .route("/vultr/registry", post(controllers::vultr::create_registry))
        .route("/vultr/registry/:id", put(controllers::vultr::update_registry))
        .route("/vultr/registry/:id", delete(controllers::vultr::delete_registry))
        .route("/vultr/registry/:id/replication", get(controllers::vultr::list_registry_replications))
        .route("/vultr/registry/:id/replication", post(controllers::vultr::create_registry_replication))
        .route("/vultr/registry/:id/replication/:replication_id", get(controllers::vultr::get_registry_replication))
        .route("/vultr/registry/:id/replication/:replication_id", delete(controllers::vultr::delete_registry_replication))
        .route("/vultr/registry/:id/repository", get(controllers::vultr::list_registry_repositories))
        .route("/vultr/registry/:id/repository/:repository_image", get(controllers::vultr::get_registry_repository))
        .route("/vultr/registry/:id/repository/:repository_image", put(controllers::vultr::update_registry_repository))
        .route("/vultr/registry/:id/repository/:repository_image", delete(controllers::vultr::delete_registry_repository))
        .route("/vultr/registry/:id/docker-credentials", post(controllers::vultr::create_registry_docker_credentials))
        .route("/vultr/registry/:id/kubernetes-docker-credentials", post(controllers::vultr::create_registry_kubernetes_docker_credentials))
        .route("/vultr/registry/:id/password", put(controllers::vultr::update_registry_password))
        .route("/vultr/registry/:id/robot", get(controllers::vultr::list_registry_robots))
        .route("/vultr/registry/:id/robot/:robot_name", get(controllers::vultr::get_registry_robot))
        .route("/vultr/registry/:id/robot/:robot_name", put(controllers::vultr::update_registry_robot))
        .route("/vultr/registry/:id/robot/:robot_name", delete(controllers::vultr::delete_registry_robot))
        .route("/vultr/registry/:id/repository/:repository_image/artifact", get(controllers::vultr::list_registry_repository_artifacts))
        .route("/vultr/registry/:id/repository/:repository_image/artifact/:artifact_digest", get(controllers::vultr::get_registry_repository_artifact))
        .route("/vultr/registry/:id/repository/:repository_image/artifact/:artifact_digest", delete(controllers::vultr::delete_registry_repository_artifact))
        .route("/vultr/registry/regions", get(controllers::vultr::list_registry_regions))
        .route("/vultr/registry/plans", get(controllers::vultr::list_registry_plans))

        // Billing
        .route("/billing", get(controllers::billing::get_billing_info))
        .route("/billing/invoices", get(controllers::billing::list_invoices))
        .route("/billing/invoices/:id", get(controllers::billing::get_invoice))
        .route("/billing/usage", get(controllers::billing::get_usage))
        
        // Admin routes
        .nest("/admin", admin_routes())
        
        // Apply authentication middleware to all protected routes
        .layer(middleware::from_fn(auth_middleware))
}

fn admin_routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/users", get(controllers::admin::list_users))
        .route("/users/:id", get(controllers::admin::get_user))
        .route("/users/:id/suspend", post(controllers::admin::suspend_user))
        .route("/users/:id/activate", post(controllers::admin::activate_user))
        .route("/instances", get(controllers::admin::list_all_instances))
        .route("/billing/overview", get(controllers::admin::billing_overview))
        .route("/metrics", get(controllers::admin::get_metrics))
        // Admin authentication middleware would be added here
}
