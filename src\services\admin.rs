use crate::{
    database::Database,
    models::{
        Instance, InstanceResponse, PaginatedResponse, PaginationQuery, User, UserProfile,
        UserStatus,
    },
    services::{ServiceError, ServiceResult},
    utils::Pagination,
};
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use mongodb::Collection;
use tracing::instrument;
use futures::TryStreamExt;

pub struct AdminService<'a> {
    users: Collection<User>,
    instances: Collection<Instance>,
}

impl<'a> AdminService<'a> {
    pub fn new(database: &Database) -> Self {
        Self {
            users: database.collection("users"),
            instances: database.collection("instances"),
        }
    }

    #[instrument(skip(self))]
    pub async fn list_all_users(
        &self,
        pagination: PaginationQuery,
    ) -> ServiceResult<PaginatedResponse<UserProfile>> {
        let page = pagination.page.unwrap_or(1);
        let per_page = pagination.per_page.unwrap_or(20);

        // Get total count
        let total = self.users.count_documents(doc! {}, None).await?;

        // Get paginated results
        let pagination_info = Pagination::new(page, per_page, total);
        let users: Vec<User> = self
            .users
            .find(doc! {}, None)
            .sort(doc! { "created_at": -1 })
            .skip(pagination_info.offset())
            .limit(pagination_info.limit())
            .await?
            .try_collect()
            .await?;

        let user_profiles: Vec<UserProfile> = users
            .into_iter()
            .map(|user| user.into())
            .collect();

        Ok(PaginatedResponse {
            data: user_profiles,
            meta: crate::models::PaginationMeta {
                page: pagination_info.page,
                per_page: pagination_info.per_page,
                total: pagination_info.total,
                total_pages: pagination_info.total_pages,
            },
        })
    }

    #[instrument(skip(self))]
    pub async fn get_user_by_id(&self, user_id: &str) -> ServiceResult<UserProfile> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        let user = self
            .users
            .find_one(doc! { "_id": user_object_id }, None)
            .await?
            .ok_or_else(|| ServiceError::NotFound("User not found".to_string()))?;

        Ok(user.into())
    }

    #[instrument(skip(self))]
    pub async fn suspend_user(&self, user_id: &str) -> ServiceResult<()> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        self.users
            .update_one(
                doc! { "_id": user_object_id },
                doc! { 
                    "$set": { 
                        "status": "Suspended",
                        "updated_at": Utc::now()
                    }
                },
                None,
            )
            .await?;

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn activate_user(&self, user_id: &str) -> ServiceResult<()> {
        let user_object_id = ObjectId::parse_str(user_id)
            .map_err(|_| ServiceError::Validation("Invalid user ID".to_string()))?;

        self.users
            .update_one(
                doc! { "_id": user_object_id },
                doc! { 
                    "$set": { 
                        "status": "Active",
                        "updated_at": Utc::now()
                    }
                },
                None,
            )
            .await?;

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_all_instances(
        &self,
        pagination: PaginationQuery,
    ) -> ServiceResult<PaginatedResponse<InstanceResponse>> {
        let page = pagination.page.unwrap_or(1);
        let per_page = pagination.per_page.unwrap_or(20);

        // Get total count
        let total = self.instances.count_documents(doc! {}, None).await?;

        // Get paginated results
        let pagination_info = Pagination::new(page, per_page, total);
        let instances: Vec<Instance> = self
            .instances
            .find(doc! {}, None)
            .sort(doc! { "created_at": -1 })
            .skip(pagination_info.offset())
            .limit(pagination_info.limit())
            .await?
            .try_collect()
            .await?;

        let instance_responses: Vec<InstanceResponse> = instances
            .into_iter()
            .map(|instance| instance.into())
            .collect();

        Ok(PaginatedResponse {
            data: instance_responses,
            meta: crate::models::PaginationMeta {
                page: pagination_info.page,
                per_page: pagination_info.per_page,
                total: pagination_info.total,
                total_pages: pagination_info.total_pages,
            },
        })
    }

    #[instrument(skip(self))]
    pub async fn get_billing_overview(&self) -> ServiceResult<serde_json::Value> {
        // Get total users
        let total_users = self.users.count_documents(doc! {}, None).await?;

        // Get active users
        let active_users = self
            .users
            .count_documents(doc! { "status": "Active" }, None)
            .await?;

        // Get total instances
        let total_instances = self.instances.count_documents(doc! {}, None).await?;

        // Get running instances
        let running_instances = self
            .instances
            .count_documents(doc! { "status": "Running" }, None)
            .await?;

        // Calculate total monthly revenue (simplified)
        let pipeline = vec![
            doc! {
                "$group": {
                    "_id": null,
                    "total_monthly_cost": { "$sum": "$monthly_cost" }
                }
            }
        ];

        let mut cursor = self.instances.aggregate(pipeline, None).await?;
        let total_monthly_revenue = if let Some(result) = cursor.try_next().await? {
            result.get_f64("total_monthly_cost").unwrap_or(0.0)
        } else {
            0.0
        };

        Ok(serde_json::json!({
            "users": {
                "total": total_users,
                "active": active_users,
                "suspended": total_users - active_users
            },
            "instances": {
                "total": total_instances,
                "running": running_instances,
                "stopped": total_instances - running_instances
            },
            "revenue": {
                "monthly_recurring": total_monthly_revenue,
                "currency": "USD"
            },
            "generated_at": Utc::now()
        }))
    }

    #[instrument(skip(self))]
    pub async fn get_system_metrics(&self) -> ServiceResult<serde_json::Value> {
        // This would integrate with your metrics system
        // For now, return basic database metrics
        
        let total_users = self.users.count_documents(doc! {}, None).await?;
        let total_instances = self.instances.count_documents(doc! {}, None).await?;

        // Get users created in the last 24 hours
        let yesterday = Utc::now() - chrono::Duration::hours(24);
        let new_users_24h = self
            .users
            .count_documents(doc! { "created_at": { "$gte": yesterday } }, None)
            .await?;

        // Get instances created in the last 24 hours
        let new_instances_24h = self
            .instances
            .count_documents(doc! { "created_at": { "$gte": yesterday } }, None)
            .await?;

        Ok(serde_json::json!({
            "database": {
                "total_users": total_users,
                "total_instances": total_instances,
                "new_users_24h": new_users_24h,
                "new_instances_24h": new_instances_24h
            },
            "system": {
                "uptime_seconds": 0, // Would be calculated from app start time
                "version": env!("CARGO_PKG_VERSION"),
                "environment": "production" // Would come from config
            },
            "generated_at": Utc::now()
        }))
    }
}
