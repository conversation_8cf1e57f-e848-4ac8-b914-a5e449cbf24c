pub mod auth;
pub mod billing;
pub mod instance;
pub mod user;

use crate::controllers::ControllerError;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum ServiceError {
    #[error("Database error: {0}")]
    Database(#[from] mongodb::error::Error),
    
    #[error("Validation error: {0}")]
    Validation(String),
    
    #[error("Authentication error: {0}")]
    Authentication(String),
    
    #[error("Authorization error: {0}")]
    Authorization(String),
    
    #[error("Not found: {0}")]
    NotFound(String),
    
    #[error("Conflict: {0}")]
    Conflict(String),
    
    #[error("External API error: {0}")]
    ExternalApi(String),
    
    #[error("Internal error: {0}")]
    Internal(String),
}

impl From<ServiceError> for ControllerError {
    fn from(err: ServiceError) -> Self {
        match err {
            ServiceError::Database(e) => ControllerError::Database(e),
            ServiceError::Validation(msg) => ControllerError::Validation(msg),
            ServiceError::Authentication(msg) => ControllerError::Authentication(msg),
            ServiceError::Authorization(msg) => ControllerError::Authorization(msg),
            ServiceError::NotFound(msg) => ControllerError::NotFound(msg),
            ServiceError::Conflict(msg) => ControllerError::Conflict(msg),
            ServiceError::ExternalApi(msg) => ControllerError::ExternalApi(msg),
            ServiceError::Internal(msg) => ControllerError::Internal(msg),
        }
    }
}

pub type ServiceResult<T> = Result<T, ServiceError>;
