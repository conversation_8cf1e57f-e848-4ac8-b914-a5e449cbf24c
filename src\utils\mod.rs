pub mod retry;

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Pagination {
    pub page: u32,
    pub per_page: u32,
    pub total: u64,
    pub total_pages: u32,
}

impl Pagination {
    pub fn new(page: u32, per_page: u32, total: u64) -> Self {
        let total_pages = ((total as f64) / (per_page as f64)).ceil() as u32;
        Self {
            page,
            per_page,
            total,
            total_pages,
        }
    }

    pub fn offset(&self) -> u64 {
        ((self.page - 1) * self.per_page) as u64
    }

    pub fn limit(&self) -> i64 {
        self.per_page as i64
    }
}

pub fn generate_invoice_number() -> String {
    let now = Utc::now();
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};

    let mut hasher = DefaultHasher::new();
    now.timestamp_nanos().hash(&mut hasher);
    let hash = hasher.finish();

    format!("INV-{}-{:06}", now.format("%Y%m"), hash % 1000000)
}

pub fn calculate_monthly_cost(hourly_rate: f64) -> f64 {
    hourly_rate * 24.0 * 30.0 // Approximate monthly hours
}

pub fn format_currency(amount: f64) -> String {
    format!("${:.2}", amount)
}
