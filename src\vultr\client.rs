use super::{models::*, VultrApiClient};
use crate::config::Config;
use anyhow::Result;
use std::sync::Arc;
use tracing::instrument;

#[derive(Clone)]
pub struct VultrClient {
    api_client: Arc<VultrApiClient>,
}

impl VultrClient {
    pub fn new(api_key: &str, config: &Config) -> Result<Self> {
        let api_client = VultrApiClient::new(api_key.to_string(), config)?;
        Ok(Self {
            api_client: Arc::new(api_client),
        })
    }

    #[instrument(skip(self))]
    pub async fn get_account(&self) -> Result<VultrAccount> {
        self.api_client.get_account().await
    }

    #[instrument(skip(self))]
    pub async fn get_account_bgp(&self) -> Result<VultrAccountBGP> {
        self.api_client.get_account_bgp().await
    }

    #[instrument(skip(self))]
    pub async fn get_account_bandwidth(&self) -> Result<VultrAccountBandwidth> {
        self.api_client.get_account_bandwidth().await
    }

    #[instrument(skip(self))]
    pub async fn list_instances(&self) -> Result<Vec<VultrInstance>> {
        self.api_client.list_instances().await
    }

    #[instrument(skip(self))]
    pub async fn get_instance(&self, instance_id: &str) -> Result<VultrInstance> {
        self.api_client.get_instance(instance_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance(&self, request: CreateVultrInstanceRequest) -> Result<VultrInstance> {
        self.api_client.create_instance(request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_instance(&self, instance_id: &str) -> Result<()> {
        self.api_client.delete_instance(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn start_instance(&self, instance_id: &str) -> Result<()> {
        self.api_client.start_instance(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn stop_instance(&self, instance_id: &str) -> Result<()> {
        self.api_client.stop_instance(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn restart_instance(&self, instance_id: &str) -> Result<()> {
        self.api_client.restart_instance(instance_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_plans(&self) -> Result<Vec<VultrPlan>> {
        self.api_client.list_plans().await
    }

    #[instrument(skip(self))]
    pub async fn list_regions(&self) -> Result<Vec<VultrRegion>> {
        self.api_client.list_regions().await
    }

    #[instrument(skip(self))]
    pub async fn list_os(&self) -> Result<Vec<VultrOS>> {
        self.api_client.list_os().await
    }

    // Backup methods
    #[instrument(skip(self))]
    pub async fn list_backups(&self) -> Result<Vec<VultrBackup>> {
        self.api_client.list_backups().await
    }

    #[instrument(skip(self))]
    pub async fn get_backup(&self, backup_id: &str) -> Result<VultrBackup> {
        self.api_client.get_backup(backup_id).await
    }

    // Bare Metal methods
    #[instrument(skip(self))]
    pub async fn list_bare_metal(&self) -> Result<Vec<VultrBareMetal>> {
        self.api_client.list_bare_metal().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_bare_metal(&self, request: CreateBareMetalRequest) -> Result<VultrBareMetal> {
        self.api_client.create_bare_metal(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal(&self, baremetal_id: &str) -> Result<VultrBareMetal> {
        self.api_client.get_bare_metal(baremetal_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_bare_metal(&self, baremetal_id: &str, request: UpdateBareMetalRequest) -> Result<VultrBareMetal> {
        self.api_client.update_bare_metal(baremetal_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_bare_metal(&self, baremetal_id: &str) -> Result<()> {
        self.api_client.delete_bare_metal(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_ipv4(&self, baremetal_id: &str) -> Result<Vec<BareMetalIpv4Info>> {
        self.api_client.get_bare_metal_ipv4(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_ipv6(&self, baremetal_id: &str) -> Result<Vec<BareMetalIpv6Info>> {
        self.api_client.get_bare_metal_ipv6(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn create_bare_metal_ipv4_reverse(&self, baremetal_id: &str, ip: &str, reverse: &str) -> Result<()> {
        self.api_client.create_bare_metal_ipv4_reverse(baremetal_id, ip, reverse).await
    }

    #[instrument(skip(self))]
    pub async fn create_bare_metal_ipv6_reverse(&self, baremetal_id: &str, ip: &str, reverse: &str) -> Result<()> {
        self.api_client.create_bare_metal_ipv6_reverse(baremetal_id, ip, reverse).await
    }

    #[instrument(skip(self))]
    pub async fn reset_bare_metal_ipv4_reverse(&self, baremetal_id: &str, ip: &str) -> Result<()> {
        self.api_client.reset_bare_metal_ipv4_reverse(baremetal_id, ip).await
    }

    #[instrument(skip(self))]
    pub async fn delete_bare_metal_ipv6_reverse(&self, baremetal_id: &str, ip: &str) -> Result<()> {
        self.api_client.delete_bare_metal_ipv6_reverse(baremetal_id, ip).await
    }

    #[instrument(skip(self))]
    pub async fn start_bare_metal(&self, baremetal_id: &str) -> Result<()> {
        self.api_client.start_bare_metal(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn reboot_bare_metal(&self, baremetal_id: &str) -> Result<()> {
        self.api_client.reboot_bare_metal(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn reinstall_bare_metal(&self, baremetal_id: &str) -> Result<VultrBareMetal> {
        self.api_client.reinstall_bare_metal(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn halt_bare_metal(&self, baremetal_id: &str) -> Result<()> {
        self.api_client.halt_bare_metal(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_bandwidth(&self, baremetal_id: &str) -> Result<BareMetalBandwidth> {
        self.api_client.get_bare_metal_bandwidth(baremetal_id).await
    }

    #[instrument(skip(self, instance_ids))]
    pub async fn halt_bare_metals(&self, instance_ids: Vec<String>) -> Result<()> {
        self.api_client.halt_bare_metals(instance_ids).await
    }

    #[instrument(skip(self, instance_ids))]
    pub async fn reboot_bare_metals(&self, instance_ids: Vec<String>) -> Result<()> {
        self.api_client.reboot_bare_metals(instance_ids).await
    }

    #[instrument(skip(self, instance_ids))]
    pub async fn start_bare_metals(&self, instance_ids: Vec<String>) -> Result<()> {
        self.api_client.start_bare_metals(instance_ids).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_user_data(&self, baremetal_id: &str) -> Result<BareMetalUserData> {
        self.api_client.get_bare_metal_user_data(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_upgrades(&self, baremetal_id: &str) -> Result<BareMetalUpgrades> {
        self.api_client.get_bare_metal_upgrades(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal_vnc(&self, baremetal_id: &str) -> Result<BareMetalVncInfo> {
        self.api_client.get_bare_metal_vnc(baremetal_id).await
    }

    #[instrument(skip(self))]
    pub async fn attach_bare_metal_vpc(&self, baremetal_id: &str, vpc_id: &str) -> Result<()> {
        self.api_client.attach_bare_metal_vpc(baremetal_id, vpc_id).await
    }

    #[instrument(skip(self))]
    pub async fn detach_bare_metal_vpc(&self, baremetal_id: &str, vpc_id: &str) -> Result<()> {
        self.api_client.detach_bare_metal_vpc(baremetal_id, vpc_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_bare_metal_vpcs(&self, baremetal_id: &str) -> Result<Vec<BareMetalVpcInfo>> {
        self.api_client.list_bare_metal_vpcs(baremetal_id).await
    }

    // Block Storage methods
    #[instrument(skip(self))]
    pub async fn list_block_storage(&self) -> Result<Vec<VultrBlockStorage>> {
        self.api_client.list_block_storage().await
    }

    #[instrument(skip(self))]
    pub async fn get_block_storage(&self, block_id: &str) -> Result<VultrBlockStorage> {
        self.api_client.get_block_storage(block_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_block_storage(&self, request: serde_json::Value) -> Result<VultrBlockStorage> {
        self.api_client.create_block_storage(request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_block_storage(&self, block_id: &str) -> Result<()> {
        self.api_client.delete_block_storage(block_id).await
    }

    // CDN methods
    #[instrument(skip(self))]
    pub async fn list_cdns(&self) -> Result<Vec<VultrCDN>> {
        self.api_client.list_cdns().await
    }

    // DNS methods
    #[instrument(skip(self))]
    pub async fn list_dns_domains(&self) -> Result<Vec<VultrDNSDomain>> {
        self.api_client.list_dns_domains().await
    }

    #[instrument(skip(self))]
    pub async fn get_dns_records(&self, domain: &str) -> Result<Vec<VultrDNSRecord>> {
        self.api_client.get_dns_records(domain).await
    }

    // Firewall methods
    #[instrument(skip(self))]
    pub async fn list_firewall_groups(&self) -> Result<Vec<VultrFirewallGroup>> {
        self.api_client.list_firewall_groups().await
    }

    #[instrument(skip(self))]
    pub async fn get_firewall_rules(&self, firewall_group_id: &str) -> Result<Vec<VultrFirewallRule>> {
        self.api_client.get_firewall_rules(firewall_group_id).await
    }

    // Kubernetes methods
    #[instrument(skip(self))]
    pub async fn list_kubernetes_clusters(&self) -> Result<Vec<VultrKubernetesCluster>> {
        self.api_client.list_kubernetes_clusters().await
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_cluster(&self, cluster_id: &str) -> Result<VultrKubernetesCluster> {
        self.api_client.get_kubernetes_cluster(cluster_id).await
    }

    // Load Balancer methods
    #[instrument(skip(self))]
    pub async fn list_load_balancers(&self) -> Result<Vec<VultrLoadBalancer>> {
        self.api_client.list_load_balancers().await
    }

    #[instrument(skip(self))]
    pub async fn get_load_balancer(&self, lb_id: &str) -> Result<VultrLoadBalancer> {
        self.api_client.get_load_balancer(lb_id).await
    }

    // Managed Database methods
    #[instrument(skip(self))]
    pub async fn list_databases(&self) -> Result<Vec<VultrManagedDatabase>> {
        self.api_client.list_databases().await
    }

    #[instrument(skip(self))]
    pub async fn get_database(&self, database_id: &str) -> Result<VultrManagedDatabase> {
        self.api_client.get_database(database_id).await
    }

    // Object Storage methods
    #[instrument(skip(self))]
    pub async fn list_object_storage(&self) -> Result<Vec<VultrObjectStorage>> {
        self.api_client.list_object_storage().await
    }

    // VPC methods
    #[instrument(skip(self))]
    pub async fn list_vpcs(&self) -> Result<Vec<VultrVPC>> {
        self.api_client.list_vpcs().await
    }

    #[instrument(skip(self))]
    pub async fn list_vpc2(&self) -> Result<Vec<VultrVPC2>> {
        self.api_client.list_vpc2().await
    }

    // Reserved IP methods
    #[instrument(skip(self))]
    pub async fn list_reserved_ips(&self) -> Result<Vec<VultrReservedIP>> {
        self.api_client.list_reserved_ips().await
    }

    // Snapshot methods
    #[instrument(skip(self))]
    pub async fn list_snapshots(&self) -> Result<Vec<VultrSnapshot>> {
        self.api_client.list_snapshots().await
    }

    #[instrument(skip(self))]
    pub async fn get_snapshot(&self, snapshot_id: &str) -> Result<VultrSnapshot> {
        self.api_client.get_snapshot(snapshot_id).await
    }

    // Sub-Account methods
    #[instrument(skip(self))]
    pub async fn list_sub_accounts(&self) -> Result<Vec<VultrSubAccount>> {
        self.api_client.list_sub_accounts().await
    }

    // SSH Key methods
    #[instrument(skip(self))]
    pub async fn list_ssh_keys(&self) -> Result<Vec<VultrSSHKey>> {
        self.api_client.list_ssh_keys().await
    }

    #[instrument(skip(self))]
    pub async fn get_ssh_key(&self, ssh_key_id: &str) -> Result<VultrSSHKey> {
        self.api_client.get_ssh_key(ssh_key_id).await
    }

    // Startup Script methods
    #[instrument(skip(self))]
    pub async fn list_startup_scripts(&self) -> Result<Vec<VultrStartupScript>> {
        self.api_client.list_startup_scripts().await
    }

    #[instrument(skip(self))]
    pub async fn get_startup_script(&self, script_id: &str) -> Result<VultrStartupScript> {
        self.api_client.get_startup_script(script_id).await
    }

    // ISO methods
    #[instrument(skip(self))]
    pub async fn list_isos(&self) -> Result<Vec<VultrISO>> {
        self.api_client.list_isos().await
    }

    #[instrument(skip(self))]
    pub async fn get_iso(&self, iso_id: &str) -> Result<VultrISO> {
        self.api_client.get_iso(iso_id).await
    }

    // Application methods
    #[instrument(skip(self))]
    pub async fn list_applications(&self) -> Result<Vec<VultrApplication>> {
        self.api_client.list_applications().await
    }

    // Marketplace methods
    #[instrument(skip(self))]
    pub async fn list_marketplace_apps(&self) -> Result<Vec<VultrMarketplaceApp>> {
        self.api_client.list_marketplace_apps().await
    }

    // Billing methods
    #[instrument(skip(self))]
    pub async fn get_billing_history(&self) -> Result<Vec<VultrBillingHistory>> {
        self.api_client.get_billing_history().await
    }

    #[instrument(skip(self))]
    pub async fn list_invoices(&self) -> Result<Vec<VultrInvoice>> {
        self.api_client.list_invoices().await
    }

    // Storage Gateway methods
    #[instrument(skip(self))]
    pub async fn list_storage_gateways(&self) -> Result<Vec<VultrStorageGateway>> {
        self.api_client.list_storage_gateways().await
    }

    // User methods
    #[instrument(skip(self))]
    pub async fn list_users(&self) -> Result<Vec<VultrUser>> {
        self.api_client.list_users().await
    }

    // Container Registry methods
    #[instrument(skip(self))]
    pub async fn list_container_registries(&self) -> Result<Vec<VultrContainerRegistry>> {
        self.api_client.list_container_registries().await
    }

    #[instrument(skip(self))]
    pub async fn get_container_registry(&self, registry_id: &str) -> Result<VultrContainerRegistry> {
        self.api_client.get_container_registry(registry_id).await
    }

    // Block Storage methods
    #[instrument(skip(self))]
    pub async fn list_block_storage(&self) -> Result<Vec<VultrBlockStorage>> {
        self.api_client.list_block_storage().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_block_storage(&self, request: CreateBlockStorageRequest) -> Result<VultrBlockStorage> {
        self.api_client.create_block_storage(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_block_storage(&self, block_id: &str) -> Result<VultrBlockStorage> {
        self.api_client.get_block_storage(block_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_block_storage(&self, block_id: &str, request: UpdateBlockStorageRequest) -> Result<VultrBlockStorage> {
        self.api_client.update_block_storage(block_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_block_storage(&self, block_id: &str) -> Result<()> {
        self.api_client.delete_block_storage(block_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn attach_block_storage(&self, block_id: &str, request: AttachBlockStorageRequest) -> Result<()> {
        self.api_client.attach_block_storage(block_id, request).await
    }

    #[instrument(skip(self, request))]
    pub async fn detach_block_storage(&self, block_id: &str, request: DetachBlockStorageRequest) -> Result<()> {
        self.api_client.detach_block_storage(block_id, request).await
    }

    // Additional Billing methods
    #[instrument(skip(self))]
    pub async fn get_invoice_items(&self, invoice_id: &str) -> Result<Vec<VultrInvoiceItem>> {
        self.api_client.get_invoice_items(invoice_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_pending_charges(&self) -> Result<VultrPendingCharges> {
        self.api_client.get_pending_charges().await
    }

    // CDN Pull Zone methods
    #[instrument(skip(self))]
    pub async fn list_pull_zones(&self) -> Result<Vec<VultrPullZone>> {
        self.api_client.list_pull_zones().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_pull_zone(&self, request: CreatePullZoneRequest) -> Result<VultrPullZone> {
        self.api_client.create_pull_zone(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_pull_zone(&self, pullzone_id: &str) -> Result<VultrPullZone> {
        self.api_client.get_pull_zone(pullzone_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_pull_zone(&self, pullzone_id: &str, request: UpdatePullZoneRequest) -> Result<VultrPullZone> {
        self.api_client.update_pull_zone(pullzone_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_pull_zone(&self, pullzone_id: &str) -> Result<()> {
        self.api_client.delete_pull_zone(pullzone_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn purge_pull_zone(&self, pullzone_id: &str, request: PurgePullZoneRequest) -> Result<()> {
        self.api_client.purge_pull_zone(pullzone_id, request).await
    }

    // CDN Push Zone methods
    #[instrument(skip(self))]
    pub async fn list_push_zones(&self) -> Result<Vec<VultrPushZone>> {
        self.api_client.list_push_zones().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_push_zone(&self, request: CreatePushZoneRequest) -> Result<VultrPushZone> {
        self.api_client.create_push_zone(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_push_zone(&self, pushzone_id: &str) -> Result<VultrPushZone> {
        self.api_client.get_push_zone(pushzone_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_push_zone(&self, pushzone_id: &str, request: UpdatePushZoneRequest) -> Result<VultrPushZone> {
        self.api_client.update_push_zone(pushzone_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_push_zone(&self, pushzone_id: &str) -> Result<()> {
        self.api_client.delete_push_zone(pushzone_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_push_zone_files(&self, pushzone_id: &str) -> Result<Vec<VultrPushZoneFile>> {
        self.api_client.get_push_zone_files(pushzone_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_push_zone_file(&self, pushzone_id: &str, file_name: &str) -> Result<()> {
        self.api_client.delete_push_zone_file(pushzone_id, file_name).await
    }

    // Enhanced Container Registry methods
    #[instrument(skip(self, request))]
    pub async fn create_registry(&self, request: CreateRegistryRequest) -> Result<VultrContainerRegistry> {
        self.api_client.create_registry(request).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry(&self, registry_id: &str, request: UpdateRegistryRequest) -> Result<VultrContainerRegistry> {
        self.api_client.update_registry(registry_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_registry(&self, registry_id: &str) -> Result<()> {
        self.api_client.delete_registry(registry_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_registry_replications(&self, registry_id: &str) -> Result<Vec<VultrRegistryReplication>> {
        self.api_client.list_registry_replications(registry_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_registry_replication(&self, registry_id: &str, request: CreateReplicationRequest) -> Result<VultrRegistryReplication> {
        self.api_client.create_registry_replication(registry_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_registry_replication(&self, registry_id: &str, replication_id: &str) -> Result<VultrRegistryReplication> {
        self.api_client.get_registry_replication(registry_id, replication_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_replication(&self, registry_id: &str, replication_id: &str) -> Result<()> {
        self.api_client.delete_registry_replication(registry_id, replication_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_registry_repositories(&self, registry_id: &str) -> Result<Vec<VultrRegistryRepository>> {
        self.api_client.list_registry_repositories(registry_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_registry_repository(&self, registry_id: &str, repository_image: &str) -> Result<VultrRegistryRepository> {
        self.api_client.get_registry_repository(registry_id, repository_image).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry_repository(&self, registry_id: &str, repository_image: &str, request: UpdateRepositoryRequest) -> Result<VultrRegistryRepository> {
        self.api_client.update_registry_repository(registry_id, repository_image, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_repository(&self, registry_id: &str, repository_image: &str) -> Result<()> {
        self.api_client.delete_registry_repository(registry_id, repository_image).await
    }

    #[instrument(skip(self))]
    pub async fn create_registry_docker_credentials(&self, registry_id: &str) -> Result<VultrDockerCredentials> {
        self.api_client.create_registry_docker_credentials(registry_id).await
    }

    #[instrument(skip(self))]
    pub async fn create_registry_kubernetes_docker_credentials(&self, registry_id: &str) -> Result<VultrKubernetesDockerCredentials> {
        self.api_client.create_registry_kubernetes_docker_credentials(registry_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry_password(&self, registry_id: &str, request: UpdateRegistryPasswordRequest) -> Result<()> {
        self.api_client.update_registry_password(registry_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn list_registry_robots(&self, registry_id: &str) -> Result<Vec<VultrRegistryRobot>> {
        self.api_client.list_registry_robots(registry_id).await
    }

    #[instrument(skip(self))]
    pub async fn get_registry_robot(&self, registry_id: &str, robot_name: &str) -> Result<VultrRegistryRobot> {
        self.api_client.get_registry_robot(registry_id, robot_name).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry_robot(&self, registry_id: &str, robot_name: &str, request: UpdateRobotRequest) -> Result<VultrRegistryRobot> {
        self.api_client.update_registry_robot(registry_id, robot_name, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_robot(&self, registry_id: &str, robot_name: &str) -> Result<()> {
        self.api_client.delete_registry_robot(registry_id, robot_name).await
    }

    #[instrument(skip(self))]
    pub async fn list_registry_repository_artifacts(&self, registry_id: &str, repository_image: &str) -> Result<Vec<VultrRegistryArtifact>> {
        self.api_client.list_registry_repository_artifacts(registry_id, repository_image).await
    }

    #[instrument(skip(self))]
    pub async fn get_registry_repository_artifact(&self, registry_id: &str, repository_image: &str, artifact_digest: &str) -> Result<VultrRegistryArtifact> {
        self.api_client.get_registry_repository_artifact(registry_id, repository_image, artifact_digest).await
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_repository_artifact(&self, registry_id: &str, repository_image: &str, artifact_digest: &str) -> Result<()> {
        self.api_client.delete_registry_repository_artifact(registry_id, repository_image, artifact_digest).await
    }

    #[instrument(skip(self))]
    pub async fn list_registry_regions(&self) -> Result<Vec<VultrRegistryRegion>> {
        self.api_client.list_registry_regions().await
    }

    #[instrument(skip(self))]
    pub async fn list_registry_plans(&self) -> Result<Vec<VultrRegistryPlan>> {
        self.api_client.list_registry_plans().await
    }

    // Enhanced DNS methods
    #[instrument(skip(self))]
    pub async fn list_dns_domains(&self) -> Result<Vec<VultrDNSDomainDetailed>> {
        self.api_client.list_dns_domains().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_dns_domain(&self, request: CreateDNSDomainRequest) -> Result<VultrDNSDomainDetailed> {
        self.api_client.create_dns_domain(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain(&self, domain: &str) -> Result<VultrDNSDomainDetailed> {
        self.api_client.get_dns_domain(domain).await
    }

    #[instrument(skip(self))]
    pub async fn delete_dns_domain(&self, domain: &str) -> Result<()> {
        self.api_client.delete_dns_domain(domain).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_dns_domain(&self, domain: &str, request: UpdateDNSDomainRequest) -> Result<()> {
        self.api_client.update_dns_domain(domain, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain_soa(&self, domain: &str) -> Result<VultrDNSSOA> {
        self.api_client.get_dns_domain_soa(domain).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_dns_domain_soa(&self, domain: &str, request: UpdateDNSSOARequest) -> Result<()> {
        self.api_client.update_dns_domain_soa(domain, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain_dnssec(&self, domain: &str) -> Result<Vec<String>> {
        self.api_client.get_dns_domain_dnssec(domain).await
    }

    #[instrument(skip(self))]
    pub async fn list_dns_domain_records(&self, domain: &str) -> Result<Vec<VultrDNSRecordDetailed>> {
        self.api_client.list_dns_domain_records(domain).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_dns_domain_record(&self, domain: &str, request: CreateDNSRecordRequest) -> Result<VultrDNSRecordDetailed> {
        self.api_client.create_dns_domain_record(domain, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain_record(&self, domain: &str, record_id: &str) -> Result<VultrDNSRecordDetailed> {
        self.api_client.get_dns_domain_record(domain, record_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_dns_domain_record(&self, domain: &str, record_id: &str, request: UpdateDNSRecordRequest) -> Result<()> {
        self.api_client.update_dns_domain_record(domain, record_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_dns_domain_record(&self, domain: &str, record_id: &str) -> Result<()> {
        self.api_client.delete_dns_domain_record(domain, record_id).await
    }

    // Enhanced Firewall methods
    #[instrument(skip(self))]
    pub async fn list_firewall_groups(&self) -> Result<Vec<VultrFirewallGroupDetailed>> {
        self.api_client.list_firewall_groups().await
    }

    #[instrument(skip(self, request))]
    pub async fn create_firewall_group(&self, request: CreateFirewallGroupRequest) -> Result<VultrFirewallGroupDetailed> {
        self.api_client.create_firewall_group(request).await
    }

    #[instrument(skip(self))]
    pub async fn get_firewall_group(&self, firewall_group_id: &str) -> Result<VultrFirewallGroupDetailed> {
        self.api_client.get_firewall_group(firewall_group_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn update_firewall_group(&self, firewall_group_id: &str, request: UpdateFirewallGroupRequest) -> Result<()> {
        self.api_client.update_firewall_group(firewall_group_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn delete_firewall_group(&self, firewall_group_id: &str) -> Result<()> {
        self.api_client.delete_firewall_group(firewall_group_id).await
    }

    #[instrument(skip(self))]
    pub async fn list_firewall_group_rules(&self, firewall_group_id: &str) -> Result<Vec<VultrFirewallRuleDetailed>> {
        self.api_client.list_firewall_group_rules(firewall_group_id).await
    }

    #[instrument(skip(self, request))]
    pub async fn create_firewall_group_rule(&self, firewall_group_id: &str, request: CreateFirewallRuleRequest) -> Result<VultrFirewallRuleDetailed> {
        self.api_client.create_firewall_group_rule(firewall_group_id, request).await
    }

    #[instrument(skip(self))]
    pub async fn get_firewall_group_rule(&self, firewall_group_id: &str, firewall_rule_id: &str) -> Result<VultrFirewallRuleDetailed> {
        self.api_client.get_firewall_group_rule(firewall_group_id, firewall_rule_id).await
    }

    #[instrument(skip(self))]
    pub async fn delete_firewall_group_rule(&self, firewall_group_id: &str, firewall_rule_id: &str) -> Result<()> {
        self.api_client.delete_firewall_group_rule(firewall_group_id, firewall_rule_id).await
    }
}
