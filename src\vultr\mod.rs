use crate::{config::Config, utils::retry::RetryClient};
use anyhow::Result;
use reqwest::{header::HeaderMap, Client, Response};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{error, info, instrument};
use futures::TryStreamExt;

pub mod client;
pub mod models;

pub use client::VultrClient;
pub use models::*;

#[derive(Debug, Clone)]
pub struct VultrApiClient {
    client: RetryClient,
    api_key: String,
    base_url: String,
}

impl VultrApiClient {
    pub fn new(api_key: String, config: &Config) -> Result<Self> {
        let mut headers = HeaderMap::new();
        headers.insert("Authorization", format!("Bearer {}", api_key).parse()?);
        headers.insert("Content-Type", "application/json".parse()?);

        let client = Client::builder()
            .default_headers(headers)
            .timeout(std::time::Duration::from_secs(30))
            .build()?;

        let retry_client = RetryClient::new(client, config.retry_config.clone());

        Ok(Self {
            client: retry_client,
            api_key,
            base_url: "https://api.vultr.com/v2".to_string(),
        })
    }

    #[instrument(skip(self))]
    pub async fn get_account(&self) -> Result<VultrAccount> {
        let url = format!("{}/account", self.base_url);
        let response = self.client.get(&url).await?;

        let account_response: VultrAccountResponse = response.json().await?;
        Ok(account_response.account)
    }

    #[instrument(skip(self))]
    pub async fn get_account_bgp(&self) -> Result<VultrAccountBGP> {
        let url = format!("{}/account/bgp", self.base_url);
        let response = self.client.get(&url).await?;

        let bgp_response: VultrAccountBGPResponse = response.json().await?;
        Ok(bgp_response.bgp)
    }

    #[instrument(skip(self))]
    pub async fn get_account_bandwidth(&self) -> Result<VultrAccountBandwidth> {
        let url = format!("{}/account/bandwidth", self.base_url);
        let response = self.client.get(&url).await?;

        let bandwidth_response: VultrAccountBandwidthResponse = response.json().await?;
        Ok(bandwidth_response.bandwidth)
    }

    #[instrument(skip(self))]
    pub async fn list_instances(&self) -> Result<Vec<VultrInstance>> {
        let url = format!("{}/instances", self.base_url);
        let response = self.client.get(&url).await?;
        
        let instances_response: VultrInstancesResponse = response.json().await?;
        Ok(instances_response.instances)
    }

    #[instrument(skip(self))]
    pub async fn get_instance(&self, instance_id: &str) -> Result<VultrInstance> {
        let url = format!("{}/instances/{}", self.base_url, instance_id);
        let response = self.client.get(&url).await?;
        
        let instance_response: VultrInstanceResponse = response.json().await?;
        Ok(instance_response.instance)
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance(&self, request: CreateVultrInstanceRequest) -> Result<VultrInstance> {
        let url = format!("{}/instances", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to create instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to create instance: {}", error_text));
        }
        
        let instance_response: VultrInstanceResponse = response.json().await?;
        info!("Instance created successfully: {}", instance_response.instance.id);
        Ok(instance_response.instance)
    }

    #[instrument(skip(self))]
    pub async fn delete_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}", self.base_url, instance_id);
        let response = self.client.delete(&url).await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to delete instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to delete instance: {}", error_text));
        }
        
        info!("Instance deleted successfully: {}", instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn start_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/start", self.base_url, instance_id);
        let response = self.client.post(&url).send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to start instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to start instance: {}", error_text));
        }
        
        info!("Instance started successfully: {}", instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn stop_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/halt", self.base_url, instance_id);
        let response = self.client.post(&url).send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to stop instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to stop instance: {}", error_text));
        }
        
        info!("Instance stopped successfully: {}", instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn restart_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/reboot", self.base_url, instance_id);
        let response = self.client.post(&url).send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to restart instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to restart instance: {}", error_text));
        }
        
        info!("Instance restarted successfully: {}", instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_plans(&self) -> Result<Vec<VultrPlan>> {
        let url = format!("{}/plans", self.base_url);
        let response = self.client.get(&url).await?;
        
        let plans_response: VultrPlansResponse = response.json().await?;
        Ok(plans_response.plans)
    }

    #[instrument(skip(self))]
    pub async fn list_regions(&self) -> Result<Vec<VultrRegion>> {
        let url = format!("{}/regions", self.base_url);
        let response = self.client.get(&url).await?;
        
        let regions_response: VultrRegionsResponse = response.json().await?;
        Ok(regions_response.regions)
    }

    #[instrument(skip(self))]
    pub async fn list_os(&self) -> Result<Vec<VultrOS>> {
        let url = format!("{}/os", self.base_url);
        let response = self.client.get(&url).await?;

        let os_response: VultrOSResponse = response.json().await?;
        Ok(os_response.os)
    }

    // Backup endpoints
    #[instrument(skip(self))]
    pub async fn list_backups(&self) -> Result<Vec<VultrBackup>> {
        let url = format!("{}/backups", self.base_url);
        let response = self.client.get(&url).await?;

        let backups_response: VultrBackupsResponse = response.json().await?;
        Ok(backups_response.backups)
    }

    #[instrument(skip(self))]
    pub async fn get_backup(&self, backup_id: &str) -> Result<VultrBackup> {
        let url = format!("{}/backups/{}", self.base_url, backup_id);
        let response = self.client.get(&url).await?;

        let backup: VultrBackup = response.json().await?;
        Ok(backup)
    }

    // Bare Metal endpoints
    #[instrument(skip(self))]
    pub async fn list_bare_metal(&self) -> Result<Vec<VultrBareMetal>> {
        let url = format!("{}/bare-metals", self.base_url);
        let response = self.client.get(&url).await?;

        let bare_metal_response: VultrBareMetalResponse = response.json().await?;
        Ok(bare_metal_response.bare_metals)
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal(&self, bare_metal_id: &str) -> Result<VultrBareMetal> {
        let url = format!("{}/bare-metals/{}", self.base_url, bare_metal_id);
        let response = self.client.get(&url).await?;

        let bare_metal: VultrBareMetal = response.json().await?;
        Ok(bare_metal)
    }

    // Block Storage endpoints
    #[instrument(skip(self))]
    pub async fn list_block_storage(&self) -> Result<Vec<VultrBlockStorage>> {
        let url = format!("{}/blocks", self.base_url);
        let response = self.client.get(&url).await?;

        let blocks_response: VultrBlockStorageResponse = response.json().await?;
        Ok(blocks_response.blocks)
    }

    #[instrument(skip(self))]
    pub async fn get_block_storage(&self, block_id: &str) -> Result<VultrBlockStorage> {
        let url = format!("{}/blocks/{}", self.base_url, block_id);
        let response = self.client.get(&url).await?;

        let block: VultrBlockStorage = response.json().await?;
        Ok(block)
    }

    #[instrument(skip(self, request))]
    pub async fn create_block_storage(&self, request: serde_json::Value) -> Result<VultrBlockStorage> {
        let url = format!("{}/blocks", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to create block storage: {}", error_text);
            return Err(anyhow::anyhow!("Failed to create block storage: {}", error_text));
        }

        let block: VultrBlockStorage = response.json().await?;
        info!("Block storage created successfully: {}", block.id);
        Ok(block)
    }

    #[instrument(skip(self))]
    pub async fn delete_block_storage(&self, block_id: &str) -> Result<()> {
        let url = format!("{}/blocks/{}", self.base_url, block_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to delete block storage: {}", error_text);
            return Err(anyhow::anyhow!("Failed to delete block storage: {}", error_text));
        }

        info!("Block storage deleted successfully: {}", block_id);
        Ok(())
    }

    // CDN endpoints
    #[instrument(skip(self))]
    pub async fn list_cdns(&self) -> Result<Vec<VultrCDN>> {
        let url = format!("{}/cdns", self.base_url);
        let response = self.client.get(&url).await?;

        let cdns_response: VultrCDNResponse = response.json().await?;
        Ok(cdns_response.cdns)
    }

    // DNS endpoints
    #[instrument(skip(self))]
    pub async fn list_dns_domains(&self) -> Result<Vec<VultrDNSDomain>> {
        let url = format!("{}/domains", self.base_url);
        let response = self.client.get(&url).await?;

        let dns_response: VultrDNSResponse = response.json().await?;
        Ok(dns_response.domains)
    }

    #[instrument(skip(self))]
    pub async fn get_dns_records(&self, domain: &str) -> Result<Vec<VultrDNSRecord>> {
        let url = format!("{}/domains/{}/records", self.base_url, domain);
        let response = self.client.get(&url).await?;

        let records: Vec<VultrDNSRecord> = response.json().await?;
        Ok(records)
    }

    // Firewall endpoints
    #[instrument(skip(self))]
    pub async fn list_firewall_groups(&self) -> Result<Vec<VultrFirewallGroup>> {
        let url = format!("{}/firewalls", self.base_url);
        let response = self.client.get(&url).await?;

        let firewalls: Vec<VultrFirewallGroup> = response.json().await?;
        Ok(firewalls)
    }

    #[instrument(skip(self))]
    pub async fn get_firewall_rules(&self, firewall_group_id: &str) -> Result<Vec<VultrFirewallRule>> {
        let url = format!("{}/firewalls/{}/rules", self.base_url, firewall_group_id);
        let response = self.client.get(&url).await?;

        let rules: Vec<VultrFirewallRule> = response.json().await?;
        Ok(rules)
    }

    // Kubernetes endpoints
    #[instrument(skip(self))]
    pub async fn list_kubernetes_clusters(&self) -> Result<Vec<VultrKubernetesCluster>> {
        let url = format!("{}/kubernetes/clusters", self.base_url);
        let response = self.client.get(&url).await?;

        let clusters: Vec<VultrKubernetesCluster> = response.json().await?;
        Ok(clusters)
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_cluster(&self, cluster_id: &str) -> Result<VultrKubernetesCluster> {
        let url = format!("{}/kubernetes/clusters/{}", self.base_url, cluster_id);
        let response = self.client.get(&url).await?;

        let cluster: VultrKubernetesCluster = response.json().await?;
        Ok(cluster)
    }

    // Load Balancer endpoints
    #[instrument(skip(self))]
    pub async fn list_load_balancers(&self) -> Result<Vec<VultrLoadBalancer>> {
        let url = format!("{}/load-balancers", self.base_url);
        let response = self.client.get(&url).await?;

        let load_balancers: Vec<VultrLoadBalancer> = response.json().await?;
        Ok(load_balancers)
    }

    #[instrument(skip(self))]
    pub async fn get_load_balancer(&self, lb_id: &str) -> Result<VultrLoadBalancer> {
        let url = format!("{}/load-balancers/{}", self.base_url, lb_id);
        let response = self.client.get(&url).await?;

        let load_balancer: VultrLoadBalancer = response.json().await?;
        Ok(load_balancer)
    }

    // Managed Database endpoints
    #[instrument(skip(self))]
    pub async fn list_databases(&self) -> Result<Vec<VultrManagedDatabase>> {
        let url = format!("{}/databases", self.base_url);
        let response = self.client.get(&url).await?;

        let databases: Vec<VultrManagedDatabase> = response.json().await?;
        Ok(databases)
    }

    #[instrument(skip(self))]
    pub async fn get_database(&self, database_id: &str) -> Result<VultrManagedDatabase> {
        let url = format!("{}/databases/{}", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let database: VultrManagedDatabase = response.json().await?;
        Ok(database)
    }

    // Object Storage (S3) endpoints
    #[instrument(skip(self))]
    pub async fn list_object_storage(&self) -> Result<Vec<VultrObjectStorage>> {
        let url = format!("{}/object-storage", self.base_url);
        let response = self.client.get(&url).await?;

        let object_storage: Vec<VultrObjectStorage> = response.json().await?;
        Ok(object_storage)
    }

    // VPC endpoints
    #[instrument(skip(self))]
    pub async fn list_vpcs(&self) -> Result<Vec<VultrVPC>> {
        let url = format!("{}/vpcs", self.base_url);
        let response = self.client.get(&url).await?;

        let vpcs: Vec<VultrVPC> = response.json().await?;
        Ok(vpcs)
    }

    #[instrument(skip(self))]
    pub async fn list_vpc2(&self) -> Result<Vec<VultrVPC2>> {
        let url = format!("{}/vpc2", self.base_url);
        let response = self.client.get(&url).await?;

        let vpc2s: Vec<VultrVPC2> = response.json().await?;
        Ok(vpc2s)
    }

    // Reserved IP endpoints
    #[instrument(skip(self))]
    pub async fn list_reserved_ips(&self) -> Result<Vec<VultrReservedIP>> {
        let url = format!("{}/reserved-ips", self.base_url);
        let response = self.client.get(&url).await?;

        let reserved_ips: Vec<VultrReservedIP> = response.json().await?;
        Ok(reserved_ips)
    }

    // Snapshot endpoints
    #[instrument(skip(self))]
    pub async fn list_snapshots(&self) -> Result<Vec<VultrSnapshot>> {
        let url = format!("{}/snapshots", self.base_url);
        let response = self.client.get(&url).await?;

        let snapshots: Vec<VultrSnapshot> = response.json().await?;
        Ok(snapshots)
    }

    #[instrument(skip(self))]
    pub async fn get_snapshot(&self, snapshot_id: &str) -> Result<VultrSnapshot> {
        let url = format!("{}/snapshots/{}", self.base_url, snapshot_id);
        let response = self.client.get(&url).await?;

        let snapshot: VultrSnapshot = response.json().await?;
        Ok(snapshot)
    }

    // Sub-Account endpoints
    #[instrument(skip(self))]
    pub async fn list_sub_accounts(&self) -> Result<Vec<VultrSubAccount>> {
        let url = format!("{}/subaccounts", self.base_url);
        let response = self.client.get(&url).await?;

        let sub_accounts: Vec<VultrSubAccount> = response.json().await?;
        Ok(sub_accounts)
    }

    // SSH Key endpoints
    #[instrument(skip(self))]
    pub async fn list_ssh_keys(&self) -> Result<Vec<VultrSSHKey>> {
        let url = format!("{}/ssh-keys", self.base_url);
        let response = self.client.get(&url).await?;

        let ssh_keys_response: VultrSSHKeysResponse = response.json().await?;
        Ok(ssh_keys_response.ssh_keys)
    }

    #[instrument(skip(self))]
    pub async fn get_ssh_key(&self, ssh_key_id: &str) -> Result<VultrSSHKey> {
        let url = format!("{}/ssh-keys/{}", self.base_url, ssh_key_id);
        let response = self.client.get(&url).await?;

        let ssh_key: VultrSSHKey = response.json().await?;
        Ok(ssh_key)
    }

    // Startup Script endpoints
    #[instrument(skip(self))]
    pub async fn list_startup_scripts(&self) -> Result<Vec<VultrStartupScript>> {
        let url = format!("{}/startup-scripts", self.base_url);
        let response = self.client.get(&url).await?;

        let startup_scripts: Vec<VultrStartupScript> = response.json().await?;
        Ok(startup_scripts)
    }

    #[instrument(skip(self))]
    pub async fn get_startup_script(&self, script_id: &str) -> Result<VultrStartupScript> {
        let url = format!("{}/startup-scripts/{}", self.base_url, script_id);
        let response = self.client.get(&url).await?;

        let startup_script: VultrStartupScript = response.json().await?;
        Ok(startup_script)
    }

    // ISO endpoints
    #[instrument(skip(self))]
    pub async fn list_isos(&self) -> Result<Vec<VultrISO>> {
        let url = format!("{}/iso", self.base_url);
        let response = self.client.get(&url).await?;

        let isos: Vec<VultrISO> = response.json().await?;
        Ok(isos)
    }

    #[instrument(skip(self))]
    pub async fn get_iso(&self, iso_id: &str) -> Result<VultrISO> {
        let url = format!("{}/iso/{}", self.base_url, iso_id);
        let response = self.client.get(&url).await?;

        let iso: VultrISO = response.json().await?;
        Ok(iso)
    }

    // Application endpoints
    #[instrument(skip(self))]
    pub async fn list_applications(&self) -> Result<Vec<VultrApplication>> {
        let url = format!("{}/applications", self.base_url);
        let response = self.client.get(&url).await?;

        let applications: Vec<VultrApplication> = response.json().await?;
        Ok(applications)
    }

    // Marketplace endpoints
    #[instrument(skip(self))]
    pub async fn list_marketplace_apps(&self) -> Result<Vec<VultrMarketplaceApp>> {
        let url = format!("{}/marketplace/apps", self.base_url);
        let response = self.client.get(&url).await?;

        let marketplace_apps: Vec<VultrMarketplaceApp> = response.json().await?;
        Ok(marketplace_apps)
    }

    // Billing endpoints
    #[instrument(skip(self))]
    pub async fn get_billing_history(&self) -> Result<Vec<VultrBillingHistory>> {
        let url = format!("{}/billing/history", self.base_url);
        let response = self.client.get(&url).await?;

        let billing_history: Vec<VultrBillingHistory> = response.json().await?;
        Ok(billing_history)
    }

    #[instrument(skip(self))]
    pub async fn list_invoices(&self) -> Result<Vec<VultrInvoice>> {
        let url = format!("{}/billing/invoices", self.base_url);
        let response = self.client.get(&url).await?;

        let invoices: Vec<VultrInvoice> = response.json().await?;
        Ok(invoices)
    }

    // Storage Gateway endpoints
    #[instrument(skip(self))]
    pub async fn list_storage_gateways(&self) -> Result<Vec<VultrStorageGateway>> {
        let url = format!("{}/storage-gateways", self.base_url);
        let response = self.client.get(&url).await?;

        let storage_gateways: Vec<VultrStorageGateway> = response.json().await?;
        Ok(storage_gateways)
    }

    // User endpoints
    #[instrument(skip(self))]
    pub async fn list_users(&self) -> Result<Vec<VultrUser>> {
        let url = format!("{}/users", self.base_url);
        let response = self.client.get(&url).await?;

        let users: Vec<VultrUser> = response.json().await?;
        Ok(users)
    }

    // Container Registry endpoints
    #[instrument(skip(self))]
    pub async fn list_container_registries(&self) -> Result<Vec<VultrContainerRegistry>> {
        let url = format!("{}/registry", self.base_url);
        let response = self.client.get(&url).await?;

        let registries: Vec<VultrContainerRegistry> = response.json().await?;
        Ok(registries)
    }

    #[instrument(skip(self))]
    pub async fn get_container_registry(&self, registry_id: &str) -> Result<VultrContainerRegistry> {
        let url = format!("{}/registry/{}", self.base_url, registry_id);
        let response = self.client.get(&url).await?;

        let registry: VultrContainerRegistry = response.json().await?;
        Ok(registry)
    }
}
