use crate::{config::Config, utils::retry::RetryClient};
use anyhow::Result;
use reqwest::{header::HeaderMap, Client, Response};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{error, info, instrument};
use futures::TryStreamExt;

pub mod client;
pub mod models;

pub use client::VultrClient;
pub use models::*;

#[derive(Debug, Clone)]
pub struct VultrApiClient {
    client: RetryClient,
    api_key: String,
    base_url: String,
}

impl VultrApiClient {
    pub fn new(api_key: String, config: &Config) -> Result<Self> {
        let mut headers = HeaderMap::new();
        headers.insert("Authorization", format!("Bearer {}", api_key).parse()?);
        headers.insert("Content-Type", "application/json".parse()?);

        let client = Client::builder()
            .default_headers(headers)
            .timeout(std::time::Duration::from_secs(30))
            .build()?;

        let retry_client = RetryClient::new(client, config.retry_config.clone());

        Ok(Self {
            client: retry_client,
            api_key,
            base_url: "https://api.vultr.com/v2".to_string(),
        })
    }

    #[instrument(skip(self))]
    pub async fn get_account(&self) -> Result<VultrAccount> {
        let url = format!("{}/account", self.base_url);
        let response = self.client.get(&url).await?;

        let account_response: VultrAccountResponse = response.json().await?;
        Ok(account_response.account)
    }

    #[instrument(skip(self))]
    pub async fn get_account_bgp(&self) -> Result<VultrAccountBGP> {
        let url = format!("{}/account/bgp", self.base_url);
        let response = self.client.get(&url).await?;

        let bgp_response: VultrAccountBGPResponse = response.json().await?;
        Ok(bgp_response.bgp)
    }

    #[instrument(skip(self))]
    pub async fn get_account_bandwidth(&self) -> Result<VultrAccountBandwidth> {
        let url = format!("{}/account/bandwidth", self.base_url);
        let response = self.client.get(&url).await?;

        let bandwidth_response: VultrAccountBandwidthResponse = response.json().await?;
        Ok(bandwidth_response.bandwidth)
    }

    #[instrument(skip(self))]
    pub async fn list_instances(&self) -> Result<Vec<VultrInstance>> {
        let url = format!("{}/instances", self.base_url);
        let response = self.client.get(&url).await?;
        
        let instances_response: VultrInstancesResponse = response.json().await?;
        Ok(instances_response.instances)
    }

    #[instrument(skip(self))]
    pub async fn get_instance(&self, instance_id: &str) -> Result<VultrInstance> {
        let url = format!("{}/instances/{}", self.base_url, instance_id);
        let response = self.client.get(&url).await?;
        
        let instance_response: VultrInstanceResponse = response.json().await?;
        Ok(instance_response.instance)
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance(&self, request: CreateVultrInstanceRequest) -> Result<VultrInstance> {
        let url = format!("{}/instances", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to create instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to create instance: {}", error_text));
        }
        
        let instance_response: VultrInstanceResponse = response.json().await?;
        info!("Instance created successfully: {}", instance_response.instance.id);
        Ok(instance_response.instance)
    }

    #[instrument(skip(self))]
    pub async fn delete_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}", self.base_url, instance_id);
        let response = self.client.delete(&url).await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to delete instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to delete instance: {}", error_text));
        }
        
        info!("Instance deleted successfully: {}", instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn start_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/start", self.base_url, instance_id);
        let response = self.client.post(&url).send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to start instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to start instance: {}", error_text));
        }
        
        info!("Instance started successfully: {}", instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn stop_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/halt", self.base_url, instance_id);
        let response = self.client.post(&url).send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to stop instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to stop instance: {}", error_text));
        }
        
        info!("Instance stopped successfully: {}", instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn restart_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/reboot", self.base_url, instance_id);
        let response = self.client.post(&url).send().await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to restart instance: {}", error_text);
            return Err(anyhow::anyhow!("Failed to restart instance: {}", error_text));
        }
        
        info!("Instance restarted successfully: {}", instance_id);
        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_plans(&self) -> Result<Vec<VultrPlan>> {
        let url = format!("{}/plans", self.base_url);
        let response = self.client.get(&url).await?;
        
        let plans_response: VultrPlansResponse = response.json().await?;
        Ok(plans_response.plans)
    }

    #[instrument(skip(self))]
    pub async fn list_regions(&self) -> Result<Vec<VultrRegion>> {
        let url = format!("{}/regions", self.base_url);
        let response = self.client.get(&url).await?;
        
        let regions_response: VultrRegionsResponse = response.json().await?;
        Ok(regions_response.regions)
    }

    #[instrument(skip(self))]
    pub async fn list_os(&self) -> Result<Vec<VultrOS>> {
        let url = format!("{}/os", self.base_url);
        let response = self.client.get(&url).await?;

        let os_response: VultrOSResponse = response.json().await?;
        Ok(os_response.os)
    }

    // Backup endpoints
    #[instrument(skip(self))]
    pub async fn list_backups(&self) -> Result<Vec<VultrBackup>> {
        let url = format!("{}/backups", self.base_url);
        let response = self.client.get(&url).await?;

        let backups_response: VultrBackupsResponse = response.json().await?;
        Ok(backups_response.backups)
    }

    #[instrument(skip(self))]
    pub async fn get_backup(&self, backup_id: &str) -> Result<VultrBackup> {
        let url = format!("{}/backups/{}", self.base_url, backup_id);
        let response = self.client.get(&url).await?;

        let backup: VultrBackup = response.json().await?;
        Ok(backup)
    }

    // Bare Metal endpoints
    #[instrument(skip(self))]
    pub async fn list_bare_metal(&self) -> Result<Vec<VultrBareMetal>> {
        let url = format!("{}/bare-metals", self.base_url);
        let response = self.client.get(&url).await?;

        let bare_metal_response: VultrBareMetalResponse = response.json().await?;
        Ok(bare_metal_response.bare_metals)
    }

    #[instrument(skip(self))]
    pub async fn get_bare_metal(&self, bare_metal_id: &str) -> Result<VultrBareMetal> {
        let url = format!("{}/bare-metals/{}", self.base_url, bare_metal_id);
        let response = self.client.get(&url).await?;

        let bare_metal: VultrBareMetal = response.json().await?;
        Ok(bare_metal)
    }

    // Block Storage endpoints
    #[instrument(skip(self))]
    pub async fn list_block_storage(&self) -> Result<Vec<VultrBlockStorage>> {
        let url = format!("{}/blocks", self.base_url);
        let response = self.client.get(&url).await?;

        let blocks_response: VultrBlockStorageResponse = response.json().await?;
        Ok(blocks_response.blocks)
    }

    #[instrument(skip(self))]
    pub async fn get_block_storage(&self, block_id: &str) -> Result<VultrBlockStorage> {
        let url = format!("{}/blocks/{}", self.base_url, block_id);
        let response = self.client.get(&url).await?;

        let block: VultrBlockStorage = response.json().await?;
        Ok(block)
    }

    #[instrument(skip(self, request))]
    pub async fn create_block_storage(&self, request: serde_json::Value) -> Result<VultrBlockStorage> {
        let url = format!("{}/blocks", self.base_url);
        let response = self.client.post(&url).json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to create block storage: {}", error_text);
            return Err(anyhow::anyhow!("Failed to create block storage: {}", error_text));
        }

        let block: VultrBlockStorage = response.json().await?;
        info!("Block storage created successfully: {}", block.id);
        Ok(block)
    }

    #[instrument(skip(self))]
    pub async fn delete_block_storage(&self, block_id: &str) -> Result<()> {
        let url = format!("{}/blocks/{}", self.base_url, block_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Failed to delete block storage: {}", error_text);
            return Err(anyhow::anyhow!("Failed to delete block storage: {}", error_text));
        }

        info!("Block storage deleted successfully: {}", block_id);
        Ok(())
    }

    // CDN endpoints
    #[instrument(skip(self))]
    pub async fn list_cdns(&self) -> Result<Vec<VultrCDN>> {
        let url = format!("{}/cdns", self.base_url);
        let response = self.client.get(&url).await?;

        let cdns_response: VultrCDNResponse = response.json().await?;
        Ok(cdns_response.cdns)
    }

    // DNS endpoints
    #[instrument(skip(self))]
    pub async fn list_dns_domains(&self) -> Result<Vec<VultrDNSDomain>> {
        let url = format!("{}/domains", self.base_url);
        let response = self.client.get(&url).await?;

        let dns_response: VultrDNSResponse = response.json().await?;
        Ok(dns_response.domains)
    }

    #[instrument(skip(self))]
    pub async fn get_dns_records(&self, domain: &str) -> Result<Vec<VultrDNSRecord>> {
        let url = format!("{}/domains/{}/records", self.base_url, domain);
        let response = self.client.get(&url).await?;

        let records: Vec<VultrDNSRecord> = response.json().await?;
        Ok(records)
    }

    // Firewall endpoints
    #[instrument(skip(self))]
    pub async fn list_firewall_groups(&self) -> Result<Vec<VultrFirewallGroup>> {
        let url = format!("{}/firewalls", self.base_url);
        let response = self.client.get(&url).await?;

        let firewalls: Vec<VultrFirewallGroup> = response.json().await?;
        Ok(firewalls)
    }

    #[instrument(skip(self))]
    pub async fn get_firewall_rules(&self, firewall_group_id: &str) -> Result<Vec<VultrFirewallRule>> {
        let url = format!("{}/firewalls/{}/rules", self.base_url, firewall_group_id);
        let response = self.client.get(&url).await?;

        let rules: Vec<VultrFirewallRule> = response.json().await?;
        Ok(rules)
    }

    // Kubernetes endpoints
    #[instrument(skip(self))]
    pub async fn list_kubernetes_clusters(&self) -> Result<Vec<VultrKubernetesCluster>> {
        let url = format!("{}/kubernetes/clusters", self.base_url);
        let response = self.client.get(&url).await?;

        let clusters: Vec<VultrKubernetesCluster> = response.json().await?;
        Ok(clusters)
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_cluster(&self, cluster_id: &str) -> Result<VultrKubernetesCluster> {
        let url = format!("{}/kubernetes/clusters/{}", self.base_url, cluster_id);
        let response = self.client.get(&url).await?;

        let cluster: VultrKubernetesCluster = response.json().await?;
        Ok(cluster)
    }

    // Load Balancer endpoints
    #[instrument(skip(self))]
    pub async fn list_load_balancers(&self) -> Result<Vec<VultrLoadBalancer>> {
        let url = format!("{}/load-balancers", self.base_url);
        let response = self.client.get(&url).await?;

        let load_balancers: Vec<VultrLoadBalancer> = response.json().await?;
        Ok(load_balancers)
    }

    #[instrument(skip(self))]
    pub async fn get_load_balancer(&self, lb_id: &str) -> Result<VultrLoadBalancer> {
        let url = format!("{}/load-balancers/{}", self.base_url, lb_id);
        let response = self.client.get(&url).await?;

        let load_balancer: VultrLoadBalancer = response.json().await?;
        Ok(load_balancer)
    }

    // Managed Database endpoints
    #[instrument(skip(self))]
    pub async fn list_databases(&self) -> Result<Vec<VultrManagedDatabase>> {
        let url = format!("{}/databases", self.base_url);
        let response = self.client.get(&url).await?;

        let databases: Vec<VultrManagedDatabase> = response.json().await?;
        Ok(databases)
    }

    #[instrument(skip(self))]
    pub async fn get_database(&self, database_id: &str) -> Result<VultrManagedDatabase> {
        let url = format!("{}/databases/{}", self.base_url, database_id);
        let response = self.client.get(&url).await?;

        let database: VultrManagedDatabase = response.json().await?;
        Ok(database)
    }

    // Object Storage (S3) endpoints
    #[instrument(skip(self))]
    pub async fn list_object_storage(&self) -> Result<Vec<VultrObjectStorage>> {
        let url = format!("{}/object-storage", self.base_url);
        let response = self.client.get(&url).await?;

        let object_storage: Vec<VultrObjectStorage> = response.json().await?;
        Ok(object_storage)
    }

    // VPC endpoints
    #[instrument(skip(self))]
    pub async fn list_vpcs(&self) -> Result<Vec<VultrVPC>> {
        let url = format!("{}/vpcs", self.base_url);
        let response = self.client.get(&url).await?;

        let vpcs: Vec<VultrVPC> = response.json().await?;
        Ok(vpcs)
    }

    #[instrument(skip(self))]
    pub async fn list_vpc2(&self) -> Result<Vec<VultrVPC2>> {
        let url = format!("{}/vpc2", self.base_url);
        let response = self.client.get(&url).await?;

        let vpc2s: Vec<VultrVPC2> = response.json().await?;
        Ok(vpc2s)
    }

    // Reserved IP endpoints
    #[instrument(skip(self))]
    pub async fn list_reserved_ips(&self) -> Result<Vec<VultrReservedIP>> {
        let url = format!("{}/reserved-ips", self.base_url);
        let response = self.client.get(&url).await?;

        let reserved_ips: Vec<VultrReservedIP> = response.json().await?;
        Ok(reserved_ips)
    }

    // Snapshot endpoints
    #[instrument(skip(self))]
    pub async fn list_snapshots(&self) -> Result<Vec<VultrSnapshot>> {
        let url = format!("{}/snapshots", self.base_url);
        let response = self.client.get(&url).await?;

        let snapshots: Vec<VultrSnapshot> = response.json().await?;
        Ok(snapshots)
    }

    #[instrument(skip(self))]
    pub async fn get_snapshot(&self, snapshot_id: &str) -> Result<VultrSnapshot> {
        let url = format!("{}/snapshots/{}", self.base_url, snapshot_id);
        let response = self.client.get(&url).await?;

        let snapshot: VultrSnapshot = response.json().await?;
        Ok(snapshot)
    }

    // Sub-Account endpoints
    #[instrument(skip(self))]
    pub async fn list_sub_accounts(&self) -> Result<Vec<VultrSubAccount>> {
        let url = format!("{}/subaccounts", self.base_url);
        let response = self.client.get(&url).await?;

        let sub_accounts: Vec<VultrSubAccount> = response.json().await?;
        Ok(sub_accounts)
    }

    // SSH Key endpoints
    #[instrument(skip(self))]
    pub async fn list_ssh_keys(&self) -> Result<Vec<VultrSSHKey>> {
        let url = format!("{}/ssh-keys", self.base_url);
        let response = self.client.get(&url).await?;

        let ssh_keys_response: VultrSSHKeysResponse = response.json().await?;
        Ok(ssh_keys_response.ssh_keys)
    }

    #[instrument(skip(self))]
    pub async fn get_ssh_key(&self, ssh_key_id: &str) -> Result<VultrSSHKey> {
        let url = format!("{}/ssh-keys/{}", self.base_url, ssh_key_id);
        let response = self.client.get(&url).await?;

        let ssh_key: VultrSSHKey = response.json().await?;
        Ok(ssh_key)
    }

    // Startup Script endpoints
    #[instrument(skip(self))]
    pub async fn list_startup_scripts(&self) -> Result<Vec<VultrStartupScript>> {
        let url = format!("{}/startup-scripts", self.base_url);
        let response = self.client.get(&url).await?;

        let startup_scripts: Vec<VultrStartupScript> = response.json().await?;
        Ok(startup_scripts)
    }

    #[instrument(skip(self))]
    pub async fn get_startup_script(&self, script_id: &str) -> Result<VultrStartupScript> {
        let url = format!("{}/startup-scripts/{}", self.base_url, script_id);
        let response = self.client.get(&url).await?;

        let startup_script: VultrStartupScript = response.json().await?;
        Ok(startup_script)
    }

    // ISO endpoints
    #[instrument(skip(self))]
    pub async fn list_isos(&self) -> Result<Vec<VultrISO>> {
        let url = format!("{}/iso", self.base_url);
        let response = self.client.get(&url).await?;

        let isos: Vec<VultrISO> = response.json().await?;
        Ok(isos)
    }

    #[instrument(skip(self))]
    pub async fn get_iso(&self, iso_id: &str) -> Result<VultrISO> {
        let url = format!("{}/iso/{}", self.base_url, iso_id);
        let response = self.client.get(&url).await?;

        let iso: VultrISO = response.json().await?;
        Ok(iso)
    }

    // Application endpoints
    #[instrument(skip(self))]
    pub async fn list_applications(&self) -> Result<Vec<VultrApplication>> {
        let url = format!("{}/applications", self.base_url);
        let response = self.client.get(&url).await?;

        let applications: Vec<VultrApplication> = response.json().await?;
        Ok(applications)
    }

    // Marketplace endpoints
    #[instrument(skip(self))]
    pub async fn list_marketplace_apps(&self) -> Result<Vec<VultrMarketplaceApp>> {
        let url = format!("{}/marketplace/apps", self.base_url);
        let response = self.client.get(&url).await?;

        let marketplace_apps: Vec<VultrMarketplaceApp> = response.json().await?;
        Ok(marketplace_apps)
    }

    // Billing endpoints
    #[instrument(skip(self))]
    pub async fn get_billing_history(&self) -> Result<Vec<VultrBillingHistory>> {
        let url = format!("{}/billing/history", self.base_url);
        let response = self.client.get(&url).await?;

        let billing_history: Vec<VultrBillingHistory> = response.json().await?;
        Ok(billing_history)
    }

    #[instrument(skip(self))]
    pub async fn list_invoices(&self) -> Result<Vec<VultrInvoice>> {
        let url = format!("{}/billing/invoices", self.base_url);
        let response = self.client.get(&url).await?;

        let invoices: Vec<VultrInvoice> = response.json().await?;
        Ok(invoices)
    }

    // Storage Gateway endpoints
    #[instrument(skip(self))]
    pub async fn list_storage_gateways(&self) -> Result<Vec<VultrStorageGateway>> {
        let url = format!("{}/storage-gateways", self.base_url);
        let response = self.client.get(&url).await?;

        let storage_gateways: Vec<VultrStorageGateway> = response.json().await?;
        Ok(storage_gateways)
    }

    // User endpoints
    #[instrument(skip(self))]
    pub async fn list_users(&self) -> Result<Vec<VultrUser>> {
        let url = format!("{}/users", self.base_url);
        let response = self.client.get(&url).await?;

        let users: Vec<VultrUser> = response.json().await?;
        Ok(users)
    }

    // Container Registry endpoints
    #[instrument(skip(self))]
    pub async fn list_container_registries(&self) -> Result<Vec<VultrContainerRegistry>> {
        let url = format!("{}/registry", self.base_url);
        let response = self.client.get(&url).await?;

        let registries: Vec<VultrContainerRegistry> = response.json().await?;
        Ok(registries)
    }

    #[instrument(skip(self))]
    pub async fn get_container_registry(&self, registry_id: &str) -> Result<VultrContainerRegistry> {
        let url = format!("{}/registry/{}", self.base_url, registry_id);
        let response = self.client.get(&url).await?;

        let registry: VultrContainerRegistry = response.json().await?;
        Ok(registry)
    }

    // Block Storage endpoints
    #[instrument(skip(self))]
    pub async fn list_block_storage(&self) -> Result<Vec<VultrBlockStorage>> {
        let url = format!("{}/blocks", self.base_url);
        let response = self.client.get(&url).await?;

        let blocks_response: VultrBlockStorageResponse = response.json().await?;
        Ok(blocks_response.blocks)
    }

    #[instrument(skip(self, request))]
    pub async fn create_block_storage(&self, request: CreateBlockStorageRequest) -> Result<VultrBlockStorage> {
        let url = format!("{}/blocks", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create block storage: {}", error_text));
        }

        let block: VultrBlockStorage = response.json().await?;
        Ok(block)
    }

    #[instrument(skip(self))]
    pub async fn get_block_storage(&self, block_id: &str) -> Result<VultrBlockStorage> {
        let url = format!("{}/blocks/{}", self.base_url, block_id);
        let response = self.client.get(&url).await?;

        let block: VultrBlockStorage = response.json().await?;
        Ok(block)
    }

    #[instrument(skip(self, request))]
    pub async fn update_block_storage(&self, block_id: &str, request: UpdateBlockStorageRequest) -> Result<VultrBlockStorage> {
        let url = format!("{}/blocks/{}", self.base_url, block_id);
        let response = self.client.put(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update block storage: {}", error_text));
        }

        let block: VultrBlockStorage = response.json().await?;
        Ok(block)
    }

    #[instrument(skip(self))]
    pub async fn delete_block_storage(&self, block_id: &str) -> Result<()> {
        let url = format!("{}/blocks/{}", self.base_url, block_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete block storage: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn attach_block_storage(&self, block_id: &str, request: AttachBlockStorageRequest) -> Result<()> {
        let url = format!("{}/blocks/{}/attach", self.base_url, block_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to attach block storage: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn detach_block_storage(&self, block_id: &str, request: DetachBlockStorageRequest) -> Result<()> {
        let url = format!("{}/blocks/{}/detach", self.base_url, block_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to detach block storage: {}", error_text));
        }

        Ok(())
    }

    // Additional Billing endpoints
    #[instrument(skip(self))]
    pub async fn get_invoice_items(&self, invoice_id: &str) -> Result<Vec<VultrInvoiceItem>> {
        let url = format!("{}/billing/invoices/{}/items", self.base_url, invoice_id);
        let response = self.client.get(&url).await?;

        let invoice_items_response: VultrInvoiceItemsResponse = response.json().await?;
        Ok(invoice_items_response.invoice_items)
    }

    #[instrument(skip(self))]
    pub async fn get_pending_charges(&self) -> Result<VultrPendingCharges> {
        let url = format!("{}/billing/pending-charges", self.base_url);
        let response = self.client.get(&url).await?;

        let pending_charges_response: VultrPendingChargesResponse = response.json().await?;
        Ok(pending_charges_response.billing)
    }

    // CDN Pull Zone endpoints
    #[instrument(skip(self))]
    pub async fn list_pull_zones(&self) -> Result<Vec<VultrPullZone>> {
        let url = format!("{}/cdns/pull-zones", self.base_url);
        let response = self.client.get(&url).await?;

        let pull_zones_response: VultrPullZoneResponse = response.json().await?;
        Ok(pull_zones_response.pullzones)
    }

    #[instrument(skip(self, request))]
    pub async fn create_pull_zone(&self, request: CreatePullZoneRequest) -> Result<VultrPullZone> {
        let url = format!("{}/cdns/pull-zones", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create pull zone: {}", error_text));
        }

        let pull_zone: VultrPullZone = response.json().await?;
        Ok(pull_zone)
    }

    #[instrument(skip(self))]
    pub async fn get_pull_zone(&self, pullzone_id: &str) -> Result<VultrPullZone> {
        let url = format!("{}/cdns/pull-zones/{}", self.base_url, pullzone_id);
        let response = self.client.get(&url).await?;

        let pull_zone: VultrPullZone = response.json().await?;
        Ok(pull_zone)
    }

    #[instrument(skip(self, request))]
    pub async fn update_pull_zone(&self, pullzone_id: &str, request: UpdatePullZoneRequest) -> Result<VultrPullZone> {
        let url = format!("{}/cdns/pull-zones/{}", self.base_url, pullzone_id);
        let response = self.client.put(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update pull zone: {}", error_text));
        }

        let pull_zone: VultrPullZone = response.json().await?;
        Ok(pull_zone)
    }

    #[instrument(skip(self))]
    pub async fn delete_pull_zone(&self, pullzone_id: &str) -> Result<()> {
        let url = format!("{}/cdns/pull-zones/{}", self.base_url, pullzone_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete pull zone: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn purge_pull_zone(&self, pullzone_id: &str, request: PurgePullZoneRequest) -> Result<()> {
        let url = format!("{}/cdns/pull-zones/{}/purge", self.base_url, pullzone_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to purge pull zone: {}", error_text));
        }

        Ok(())
    }

    // CDN Push Zone endpoints
    #[instrument(skip(self))]
    pub async fn list_push_zones(&self) -> Result<Vec<VultrPushZone>> {
        let url = format!("{}/cdns/push-zones", self.base_url);
        let response = self.client.get(&url).await?;

        let push_zones_response: VultrPushZoneResponse = response.json().await?;
        Ok(push_zones_response.pushzones)
    }

    #[instrument(skip(self, request))]
    pub async fn create_push_zone(&self, request: CreatePushZoneRequest) -> Result<VultrPushZone> {
        let url = format!("{}/cdns/push-zones", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create push zone: {}", error_text));
        }

        let push_zone: VultrPushZone = response.json().await?;
        Ok(push_zone)
    }

    #[instrument(skip(self))]
    pub async fn get_push_zone(&self, pushzone_id: &str) -> Result<VultrPushZone> {
        let url = format!("{}/cdns/push-zones/{}", self.base_url, pushzone_id);
        let response = self.client.get(&url).await?;

        let push_zone: VultrPushZone = response.json().await?;
        Ok(push_zone)
    }

    #[instrument(skip(self, request))]
    pub async fn update_push_zone(&self, pushzone_id: &str, request: UpdatePushZoneRequest) -> Result<VultrPushZone> {
        let url = format!("{}/cdns/push-zones/{}", self.base_url, pushzone_id);
        let response = self.client.put(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update push zone: {}", error_text));
        }

        let push_zone: VultrPushZone = response.json().await?;
        Ok(push_zone)
    }

    #[instrument(skip(self))]
    pub async fn delete_push_zone(&self, pushzone_id: &str) -> Result<()> {
        let url = format!("{}/cdns/push-zones/{}", self.base_url, pushzone_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete push zone: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_push_zone_files(&self, pushzone_id: &str) -> Result<Vec<VultrPushZoneFile>> {
        let url = format!("{}/cdns/push-zones/{}/files", self.base_url, pushzone_id);
        let response = self.client.get(&url).await?;

        let files_response: VultrPushZoneFilesResponse = response.json().await?;
        Ok(files_response.files)
    }

    #[instrument(skip(self))]
    pub async fn delete_push_zone_file(&self, pushzone_id: &str, file_name: &str) -> Result<()> {
        let url = format!("{}/cdns/push-zones/{}/files/{}", self.base_url, pushzone_id, file_name);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete push zone file: {}", error_text));
        }

        Ok(())
    }

    // Enhanced Container Registry endpoints
    #[instrument(skip(self, request))]
    pub async fn create_registry(&self, request: CreateRegistryRequest) -> Result<VultrContainerRegistry> {
        let url = format!("{}/registry", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create registry: {}", error_text));
        }

        let registry: VultrContainerRegistry = response.json().await?;
        Ok(registry)
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry(&self, registry_id: &str, request: UpdateRegistryRequest) -> Result<VultrContainerRegistry> {
        let url = format!("{}/registry/{}", self.base_url, registry_id);
        let response = self.client.put(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update registry: {}", error_text));
        }

        let registry: VultrContainerRegistry = response.json().await?;
        Ok(registry)
    }

    #[instrument(skip(self))]
    pub async fn delete_registry(&self, registry_id: &str) -> Result<()> {
        let url = format!("{}/registry/{}", self.base_url, registry_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete registry: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_registry_replications(&self, registry_id: &str) -> Result<Vec<VultrRegistryReplication>> {
        let url = format!("{}/registry/{}/replication", self.base_url, registry_id);
        let response = self.client.get(&url).await?;

        let replications_response: VultrRegistryReplicationResponse = response.json().await?;
        Ok(replications_response.replications)
    }

    #[instrument(skip(self, request))]
    pub async fn create_registry_replication(&self, registry_id: &str, request: CreateReplicationRequest) -> Result<VultrRegistryReplication> {
        let url = format!("{}/registry/{}/replication", self.base_url, registry_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create replication: {}", error_text));
        }

        let replication: VultrRegistryReplication = response.json().await?;
        Ok(replication)
    }

    #[instrument(skip(self))]
    pub async fn get_registry_replication(&self, registry_id: &str, replication_id: &str) -> Result<VultrRegistryReplication> {
        let url = format!("{}/registry/{}/replication/{}", self.base_url, registry_id, replication_id);
        let response = self.client.get(&url).await?;

        let replication: VultrRegistryReplication = response.json().await?;
        Ok(replication)
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_replication(&self, registry_id: &str, replication_id: &str) -> Result<()> {
        let url = format!("{}/registry/{}/replication/{}", self.base_url, registry_id, replication_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete replication: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_registry_repositories(&self, registry_id: &str) -> Result<Vec<VultrRegistryRepository>> {
        let url = format!("{}/registry/{}/repository", self.base_url, registry_id);
        let response = self.client.get(&url).await?;

        let repositories_response: VultrRegistryRepositoryResponse = response.json().await?;
        Ok(repositories_response.repositories)
    }

    #[instrument(skip(self))]
    pub async fn get_registry_repository(&self, registry_id: &str, repository_image: &str) -> Result<VultrRegistryRepository> {
        let url = format!("{}/registry/{}/repository/{}", self.base_url, registry_id, repository_image);
        let response = self.client.get(&url).await?;

        let repository: VultrRegistryRepository = response.json().await?;
        Ok(repository)
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry_repository(&self, registry_id: &str, repository_image: &str, request: UpdateRepositoryRequest) -> Result<VultrRegistryRepository> {
        let url = format!("{}/registry/{}/repository/{}", self.base_url, registry_id, repository_image);
        let response = self.client.put(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update repository: {}", error_text));
        }

        let repository: VultrRegistryRepository = response.json().await?;
        Ok(repository)
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_repository(&self, registry_id: &str, repository_image: &str) -> Result<()> {
        let url = format!("{}/registry/{}/repository/{}", self.base_url, registry_id, repository_image);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete repository: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn create_registry_docker_credentials(&self, registry_id: &str) -> Result<VultrDockerCredentials> {
        let url = format!("{}/registry/{}/docker-credentials", self.base_url, registry_id);
        let response = self.client.post(&url).await.send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create docker credentials: {}", error_text));
        }

        let credentials: VultrDockerCredentials = response.json().await?;
        Ok(credentials)
    }

    #[instrument(skip(self))]
    pub async fn create_registry_kubernetes_docker_credentials(&self, registry_id: &str) -> Result<VultrKubernetesDockerCredentials> {
        let url = format!("{}/registry/{}/kubernetes-docker-credentials", self.base_url, registry_id);
        let response = self.client.post(&url).await.send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create kubernetes docker credentials: {}", error_text));
        }

        let credentials: VultrKubernetesDockerCredentials = response.json().await?;
        Ok(credentials)
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry_password(&self, registry_id: &str, request: UpdateRegistryPasswordRequest) -> Result<()> {
        let url = format!("{}/registry/{}/password", self.base_url, registry_id);
        let response = self.client.put(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update registry password: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_registry_robots(&self, registry_id: &str) -> Result<Vec<VultrRegistryRobot>> {
        let url = format!("{}/registry/{}/robot", self.base_url, registry_id);
        let response = self.client.get(&url).await?;

        let robots_response: VultrRegistryRobotResponse = response.json().await?;
        Ok(robots_response.robots)
    }

    #[instrument(skip(self))]
    pub async fn get_registry_robot(&self, registry_id: &str, robot_name: &str) -> Result<VultrRegistryRobot> {
        let url = format!("{}/registry/{}/robot/{}", self.base_url, registry_id, robot_name);
        let response = self.client.get(&url).await?;

        let robot: VultrRegistryRobot = response.json().await?;
        Ok(robot)
    }

    #[instrument(skip(self, request))]
    pub async fn update_registry_robot(&self, registry_id: &str, robot_name: &str, request: UpdateRobotRequest) -> Result<VultrRegistryRobot> {
        let url = format!("{}/registry/{}/robot/{}", self.base_url, registry_id, robot_name);
        let response = self.client.put(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update robot: {}", error_text));
        }

        let robot: VultrRegistryRobot = response.json().await?;
        Ok(robot)
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_robot(&self, registry_id: &str, robot_name: &str) -> Result<()> {
        let url = format!("{}/registry/{}/robot/{}", self.base_url, registry_id, robot_name);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete robot: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_registry_repository_artifacts(&self, registry_id: &str, repository_image: &str) -> Result<Vec<VultrRegistryArtifact>> {
        let url = format!("{}/registry/{}/repository/{}/artifact", self.base_url, registry_id, repository_image);
        let response = self.client.get(&url).await?;

        let artifacts_response: VultrRegistryArtifactResponse = response.json().await?;
        Ok(artifacts_response.artifacts)
    }

    #[instrument(skip(self))]
    pub async fn get_registry_repository_artifact(&self, registry_id: &str, repository_image: &str, artifact_digest: &str) -> Result<VultrRegistryArtifact> {
        let url = format!("{}/registry/{}/repository/{}/artifact/{}", self.base_url, registry_id, repository_image, artifact_digest);
        let response = self.client.get(&url).await?;

        let artifact: VultrRegistryArtifact = response.json().await?;
        Ok(artifact)
    }

    #[instrument(skip(self))]
    pub async fn delete_registry_repository_artifact(&self, registry_id: &str, repository_image: &str, artifact_digest: &str) -> Result<()> {
        let url = format!("{}/registry/{}/repository/{}/artifact/{}", self.base_url, registry_id, repository_image, artifact_digest);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete artifact: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_registry_regions(&self) -> Result<Vec<VultrRegistryRegion>> {
        let url = format!("{}/registry/region/list", self.base_url);
        let response = self.client.get(&url).await?;

        let regions_response: VultrRegistryRegionResponse = response.json().await?;
        Ok(regions_response.regions)
    }

    #[instrument(skip(self))]
    pub async fn list_registry_plans(&self) -> Result<Vec<VultrRegistryPlan>> {
        let url = format!("{}/registry/plan/list", self.base_url);
        let response = self.client.get(&url).await?;

        let plans_response: VultrRegistryPlanResponse = response.json().await?;
        Ok(plans_response.plans)
    }

    // Enhanced DNS endpoints
    #[instrument(skip(self))]
    pub async fn list_dns_domains(&self) -> Result<Vec<VultrDNSDomainDetailed>> {
        let url = format!("{}/domains", self.base_url);
        let response = self.client.get(&url).await?;

        let domains_response: VultrDNSDomainsResponse = response.json().await?;
        Ok(domains_response.domains)
    }

    #[instrument(skip(self, request))]
    pub async fn create_dns_domain(&self, request: CreateDNSDomainRequest) -> Result<VultrDNSDomainDetailed> {
        let url = format!("{}/domains", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create DNS domain: {}", error_text));
        }

        let domain: VultrDNSDomainDetailed = response.json().await?;
        Ok(domain)
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain(&self, domain: &str) -> Result<VultrDNSDomainDetailed> {
        let url = format!("{}/domains/{}", self.base_url, domain);
        let response = self.client.get(&url).await?;

        let domain: VultrDNSDomainDetailed = response.json().await?;
        Ok(domain)
    }

    #[instrument(skip(self))]
    pub async fn delete_dns_domain(&self, domain: &str) -> Result<()> {
        let url = format!("{}/domains/{}", self.base_url, domain);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete DNS domain: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn update_dns_domain(&self, domain: &str, request: UpdateDNSDomainRequest) -> Result<()> {
        let url = format!("{}/domains/{}", self.base_url, domain);
        let response = self.client.put(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update DNS domain: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain_soa(&self, domain: &str) -> Result<VultrDNSSOA> {
        let url = format!("{}/domains/{}/soa", self.base_url, domain);
        let response = self.client.get(&url).await?;

        let soa_response: VultrDNSSOAResponse = response.json().await?;
        Ok(soa_response.dns_soa)
    }

    #[instrument(skip(self, request))]
    pub async fn update_dns_domain_soa(&self, domain: &str, request: UpdateDNSSOARequest) -> Result<()> {
        let url = format!("{}/domains/{}/soa", self.base_url, domain);
        let response = self.client.patch(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update DNS SOA: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain_dnssec(&self, domain: &str) -> Result<Vec<String>> {
        let url = format!("{}/domains/{}/dnssec", self.base_url, domain);
        let response = self.client.get(&url).await?;

        let dnssec_response: VultrDNSSecResponse = response.json().await?;
        Ok(dnssec_response.dns_sec)
    }

    #[instrument(skip(self))]
    pub async fn list_dns_domain_records(&self, domain: &str) -> Result<Vec<VultrDNSRecordDetailed>> {
        let url = format!("{}/domains/{}/records", self.base_url, domain);
        let response = self.client.get(&url).await?;

        let records_response: VultrDNSRecordsResponse = response.json().await?;
        Ok(records_response.records)
    }

    #[instrument(skip(self, request))]
    pub async fn create_dns_domain_record(&self, domain: &str, request: CreateDNSRecordRequest) -> Result<VultrDNSRecordDetailed> {
        let url = format!("{}/domains/{}/records", self.base_url, domain);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create DNS record: {}", error_text));
        }

        let record: VultrDNSRecordDetailed = response.json().await?;
        Ok(record)
    }

    #[instrument(skip(self))]
    pub async fn get_dns_domain_record(&self, domain: &str, record_id: &str) -> Result<VultrDNSRecordDetailed> {
        let url = format!("{}/domains/{}/records/{}", self.base_url, domain, record_id);
        let response = self.client.get(&url).await?;

        let record: VultrDNSRecordDetailed = response.json().await?;
        Ok(record)
    }

    #[instrument(skip(self, request))]
    pub async fn update_dns_domain_record(&self, domain: &str, record_id: &str, request: UpdateDNSRecordRequest) -> Result<()> {
        let url = format!("{}/domains/{}/records/{}", self.base_url, domain, record_id);
        let response = self.client.patch(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update DNS record: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_dns_domain_record(&self, domain: &str, record_id: &str) -> Result<()> {
        let url = format!("{}/domains/{}/records/{}", self.base_url, domain, record_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete DNS record: {}", error_text));
        }

        Ok(())
    }

    // Enhanced Firewall endpoints
    #[instrument(skip(self))]
    pub async fn list_firewall_groups(&self) -> Result<Vec<VultrFirewallGroupDetailed>> {
        let url = format!("{}/firewalls", self.base_url);
        let response = self.client.get(&url).await?;

        let firewall_groups_response: VultrFirewallGroupsResponse = response.json().await?;
        Ok(firewall_groups_response.firewall_groups)
    }

    #[instrument(skip(self, request))]
    pub async fn create_firewall_group(&self, request: CreateFirewallGroupRequest) -> Result<VultrFirewallGroupDetailed> {
        let url = format!("{}/firewalls", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create firewall group: {}", error_text));
        }

        let firewall_group: VultrFirewallGroupDetailed = response.json().await?;
        Ok(firewall_group)
    }

    #[instrument(skip(self))]
    pub async fn get_firewall_group(&self, firewall_group_id: &str) -> Result<VultrFirewallGroupDetailed> {
        let url = format!("{}/firewalls/{}", self.base_url, firewall_group_id);
        let response = self.client.get(&url).await?;

        let firewall_group: VultrFirewallGroupDetailed = response.json().await?;
        Ok(firewall_group)
    }

    #[instrument(skip(self, request))]
    pub async fn update_firewall_group(&self, firewall_group_id: &str, request: UpdateFirewallGroupRequest) -> Result<()> {
        let url = format!("{}/firewalls/{}", self.base_url, firewall_group_id);
        let response = self.client.put(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update firewall group: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_firewall_group(&self, firewall_group_id: &str) -> Result<()> {
        let url = format!("{}/firewalls/{}", self.base_url, firewall_group_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete firewall group: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_firewall_group_rules(&self, firewall_group_id: &str) -> Result<Vec<VultrFirewallRuleDetailed>> {
        let url = format!("{}/firewalls/{}/rules", self.base_url, firewall_group_id);
        let response = self.client.get(&url).await?;

        let firewall_rules_response: VultrFirewallRulesResponse = response.json().await?;
        Ok(firewall_rules_response.firewall_rules)
    }

    #[instrument(skip(self, request))]
    pub async fn create_firewall_group_rule(&self, firewall_group_id: &str, request: CreateFirewallRuleRequest) -> Result<VultrFirewallRuleDetailed> {
        let url = format!("{}/firewalls/{}/rules", self.base_url, firewall_group_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create firewall rule: {}", error_text));
        }

        let firewall_rule: VultrFirewallRuleDetailed = response.json().await?;
        Ok(firewall_rule)
    }

    #[instrument(skip(self))]
    pub async fn get_firewall_group_rule(&self, firewall_group_id: &str, firewall_rule_id: &str) -> Result<VultrFirewallRuleDetailed> {
        let url = format!("{}/firewalls/{}/rules/{}", self.base_url, firewall_group_id, firewall_rule_id);
        let response = self.client.get(&url).await?;

        let firewall_rule: VultrFirewallRuleDetailed = response.json().await?;
        Ok(firewall_rule)
    }

    #[instrument(skip(self))]
    pub async fn delete_firewall_group_rule(&self, firewall_group_id: &str, firewall_rule_id: &str) -> Result<()> {
        let url = format!("{}/firewalls/{}/rules/{}", self.base_url, firewall_group_id, firewall_rule_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete firewall rule: {}", error_text));
        }

        Ok(())
    }

    // Enhanced Instance endpoints
    #[instrument(skip(self))]
    pub async fn list_instances_detailed(&self) -> Result<Vec<VultrInstanceDetailed>> {
        let url = format!("{}/instances", self.base_url);
        let response = self.client.get(&url).await?;

        let instances_response: VultrInstancesResponse = response.json().await?;
        Ok(instances_response.instances)
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_detailed(&self, request: CreateInstanceRequest) -> Result<VultrInstanceDetailed> {
        let url = format!("{}/instances", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create instance: {}", error_text));
        }

        let instance: VultrInstanceDetailed = response.json().await?;
        Ok(instance)
    }

    #[instrument(skip(self))]
    pub async fn get_instance_detailed(&self, instance_id: &str) -> Result<VultrInstanceDetailed> {
        let url = format!("{}/instances/{}", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let instance: VultrInstanceDetailed = response.json().await?;
        Ok(instance)
    }

    #[instrument(skip(self, request))]
    pub async fn update_instance_detailed(&self, instance_id: &str, request: UpdateInstanceRequest) -> Result<VultrInstanceDetailed> {
        let url = format!("{}/instances/{}", self.base_url, instance_id);
        let response = self.client.patch(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update instance: {}", error_text));
        }

        let instance: VultrInstanceDetailed = response.json().await?;
        Ok(instance)
    }

    #[instrument(skip(self))]
    pub async fn delete_instance_detailed(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}", self.base_url, instance_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn halt_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/halt", self.base_url, instance_id);
        let response = self.client.post(&url).await.send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to halt instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn halt_instances(&self, request: BulkInstanceActionRequest) -> Result<()> {
        let url = format!("{}/instances/halt", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to halt instances: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn reboot_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/reboot", self.base_url, instance_id);
        let response = self.client.post(&url).await.send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to reboot instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn reboot_instances(&self, request: BulkInstanceActionRequest) -> Result<()> {
        let url = format!("{}/instances/reboot", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to reboot instances: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn start_instance(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/start", self.base_url, instance_id);
        let response = self.client.post(&url).await.send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to start instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn start_instances(&self, request: BulkInstanceActionRequest) -> Result<()> {
        let url = format!("{}/instances/start", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to start instances: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn reinstall_instance(&self, instance_id: &str, request: Option<serde_json::Value>) -> Result<VultrInstanceDetailed> {
        let url = format!("{}/instances/{}/reinstall", self.base_url, instance_id);
        let response = if let Some(req) = request {
            self.client.post(&url).await.json(&req).send().await?
        } else {
            self.client.post(&url).await.send().await?
        };

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to reinstall instance: {}", error_text));
        }

        let instance: VultrInstanceDetailed = response.json().await?;
        Ok(instance)
    }

    #[instrument(skip(self))]
    pub async fn get_instance_bandwidth(&self, instance_id: &str) -> Result<HashMap<String, InstanceBandwidth>> {
        let url = format!("{}/instances/{}/bandwidth", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let bandwidth_response: InstanceBandwidthResponse = response.json().await?;
        Ok(bandwidth_response.bandwidth)
    }

    #[instrument(skip(self))]
    pub async fn get_instance_neighbors(&self, instance_id: &str) -> Result<Vec<InstanceNeighbor>> {
        let url = format!("{}/instances/{}/neighbors", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let neighbors_response: InstanceNeighborsResponse = response.json().await?;
        Ok(neighbors_response.neighbors)
    }

    #[instrument(skip(self))]
    pub async fn list_instance_vpcs(&self, instance_id: &str) -> Result<Vec<InstanceVPC>> {
        let url = format!("{}/instances/{}/vpcs", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let vpcs_response: InstanceVPCsResponse = response.json().await?;
        Ok(vpcs_response.vpcs)
    }

    #[instrument(skip(self))]
    pub async fn get_instance_iso_status(&self, instance_id: &str) -> Result<InstanceISOStatus> {
        let url = format!("{}/instances/{}/iso", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let iso_status: InstanceISOStatus = response.json().await?;
        Ok(iso_status)
    }

    #[instrument(skip(self, request))]
    pub async fn attach_instance_iso(&self, instance_id: &str, request: AttachISORequest) -> Result<()> {
        let url = format!("{}/instances/{}/iso/attach", self.base_url, instance_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to attach ISO: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn detach_instance_iso(&self, instance_id: &str) -> Result<()> {
        let url = format!("{}/instances/{}/iso/detach", self.base_url, instance_id);
        let response = self.client.post(&url).await.send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to detach ISO: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn attach_instance_vpc(&self, instance_id: &str, request: AttachVPCRequest) -> Result<()> {
        let url = format!("{}/instances/{}/vpcs/attach", self.base_url, instance_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to attach VPC: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn detach_instance_vpc(&self, instance_id: &str, request: DetachVPCRequest) -> Result<()> {
        let url = format!("{}/instances/{}/vpcs/detach", self.base_url, instance_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to detach VPC: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_backup_schedule(&self, instance_id: &str, request: CreateBackupScheduleRequest) -> Result<()> {
        let url = format!("{}/instances/{}/backup-schedule", self.base_url, instance_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create backup schedule: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_instance_backup_schedule(&self, instance_id: &str) -> Result<InstanceBackupSchedule> {
        let url = format!("{}/instances/{}/backup-schedule", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let backup_schedule: InstanceBackupSchedule = response.json().await?;
        Ok(backup_schedule)
    }

    #[instrument(skip(self, request))]
    pub async fn restore_instance(&self, instance_id: &str, request: RestoreInstanceRequest) -> Result<()> {
        let url = format!("{}/instances/{}/restore", self.base_url, instance_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to restore instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_instance_ipv4(&self, instance_id: &str) -> Result<Vec<InstanceIPv4>> {
        let url = format!("{}/instances/{}/ipv4", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let ipv4_response: InstanceIPv4Response = response.json().await?;
        Ok(ipv4_response.ipv4s)
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_ipv4(&self, instance_id: &str, request: CreateInstanceIPv4Request) -> Result<InstanceIPv4> {
        let url = format!("{}/instances/{}/ipv4", self.base_url, instance_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create IPv4: {}", error_text));
        }

        let ipv4: InstanceIPv4 = response.json().await?;
        Ok(ipv4)
    }

    #[instrument(skip(self))]
    pub async fn delete_instance_ipv4(&self, instance_id: &str, ipv4: &str) -> Result<()> {
        let url = format!("{}/instances/{}/ipv4/{}", self.base_url, instance_id, ipv4);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete IPv4: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_instance_ipv6(&self, instance_id: &str) -> Result<Vec<InstanceIPv6>> {
        let url = format!("{}/instances/{}/ipv6", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let ipv6_response: InstanceIPv6Response = response.json().await?;
        Ok(ipv6_response.ipv6s)
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_reverse_ipv6(&self, instance_id: &str, request: CreateReverseIPv6Request) -> Result<()> {
        let url = format!("{}/instances/{}/ipv6/reverse", self.base_url, instance_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create reverse IPv6: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_instance_ipv6_reverse(&self, instance_id: &str) -> Result<Vec<InstanceIPv6Reverse>> {
        let url = format!("{}/instances/{}/ipv6/reverse", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let reverse_response: InstanceIPv6ReverseResponse = response.json().await?;
        Ok(reverse_response.reverse_ipv6s)
    }

    #[instrument(skip(self))]
    pub async fn delete_instance_reverse_ipv6(&self, instance_id: &str, ipv6: &str) -> Result<()> {
        let url = format!("{}/instances/{}/ipv6/reverse/{}", self.base_url, instance_id, ipv6);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete reverse IPv6: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn create_instance_reverse_ipv4(&self, instance_id: &str, request: CreateReverseIPv4Request) -> Result<()> {
        let url = format!("{}/instances/{}/ipv4/reverse", self.base_url, instance_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create reverse IPv4: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn set_instance_default_reverse_ipv4(&self, instance_id: &str, ip: &str) -> Result<()> {
        let url = format!("{}/instances/{}/ipv4/reverse/default", self.base_url, instance_id);
        let request = serde_json::json!({ "ip": ip });
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to set default reverse IPv4: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_instance_userdata(&self, instance_id: &str) -> Result<InstanceUserData> {
        let url = format!("{}/instances/{}/user-data", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let userdata: InstanceUserData = response.json().await?;
        Ok(userdata)
    }

    #[instrument(skip(self))]
    pub async fn get_instance_upgrades(&self, instance_id: &str) -> Result<InstanceUpgrades> {
        let url = format!("{}/instances/{}/upgrades", self.base_url, instance_id);
        let response = self.client.get(&url).await?;

        let upgrades: InstanceUpgrades = response.json().await?;
        Ok(upgrades)
    }

    #[instrument(skip(self))]
    pub async fn get_instance_job(&self, instance_id: &str, job_id: &str) -> Result<InstanceJob> {
        let url = format!("{}/instances/{}/jobs/{}", self.base_url, instance_id, job_id);
        let response = self.client.get(&url).await?;

        let job: InstanceJob = response.json().await?;
        Ok(job)
    }

    // ISO endpoints
    #[instrument(skip(self))]
    pub async fn list_isos(&self) -> Result<Vec<VultrISO>> {
        let url = format!("{}/iso", self.base_url);
        let response = self.client.get(&url).await?;

        let isos_response: VultrISOsResponse = response.json().await?;
        Ok(isos_response.isos)
    }

    #[instrument(skip(self, request))]
    pub async fn create_iso(&self, request: CreateISORequest) -> Result<VultrISO> {
        let url = format!("{}/iso", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create ISO: {}", error_text));
        }

        let iso: VultrISO = response.json().await?;
        Ok(iso)
    }

    #[instrument(skip(self))]
    pub async fn get_iso(&self, iso_id: &str) -> Result<VultrISO> {
        let url = format!("{}/iso/{}", self.base_url, iso_id);
        let response = self.client.get(&url).await?;

        let iso: VultrISO = response.json().await?;
        Ok(iso)
    }

    #[instrument(skip(self))]
    pub async fn delete_iso(&self, iso_id: &str) -> Result<()> {
        let url = format!("{}/iso/{}", self.base_url, iso_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete ISO: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_public_isos(&self) -> Result<Vec<VultrPublicISO>> {
        let url = format!("{}/iso-public", self.base_url);
        let response = self.client.get(&url).await?;

        let public_isos_response: VultrPublicISOsResponse = response.json().await?;
        Ok(public_isos_response.public_isos)
    }

    // Enhanced Kubernetes endpoints
    #[instrument(skip(self, request))]
    pub async fn create_kubernetes_cluster(&self, request: CreateKubernetesClusterRequest) -> Result<VultrKubernetesClusterDetailed> {
        let url = format!("{}/kubernetes/clusters", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create Kubernetes cluster: {}", error_text));
        }

        let cluster: VultrKubernetesClusterDetailed = response.json().await?;
        Ok(cluster)
    }

    #[instrument(skip(self))]
    pub async fn list_kubernetes_clusters(&self) -> Result<Vec<VultrKubernetesClusterDetailed>> {
        let url = format!("{}/kubernetes/clusters", self.base_url);
        let response = self.client.get(&url).await?;

        let clusters_response: VultrKubernetesClustersResponse = response.json().await?;
        Ok(clusters_response.vke_clusters)
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_cluster(&self, vke_id: &str) -> Result<VultrKubernetesClusterDetailed> {
        let url = format!("{}/kubernetes/clusters/{}", self.base_url, vke_id);
        let response = self.client.get(&url).await?;

        let cluster: VultrKubernetesClusterDetailed = response.json().await?;
        Ok(cluster)
    }

    #[instrument(skip(self, request))]
    pub async fn update_kubernetes_cluster(&self, vke_id: &str, request: UpdateKubernetesClusterRequest) -> Result<VultrKubernetesClusterDetailed> {
        let url = format!("{}/kubernetes/clusters/{}", self.base_url, vke_id);
        let response = self.client.put(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update Kubernetes cluster: {}", error_text));
        }

        let cluster: VultrKubernetesClusterDetailed = response.json().await?;
        Ok(cluster)
    }

    #[instrument(skip(self))]
    pub async fn delete_kubernetes_cluster(&self, vke_id: &str) -> Result<()> {
        let url = format!("{}/kubernetes/clusters/{}", self.base_url, vke_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete Kubernetes cluster: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_kubernetes_cluster_with_resources(&self, vke_id: &str) -> Result<()> {
        let url = format!("{}/kubernetes/clusters/{}/delete-with-linked-resources", self.base_url, vke_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete Kubernetes cluster with resources: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_resources(&self, vke_id: &str) -> Result<VultrKubernetesResources> {
        let url = format!("{}/kubernetes/clusters/{}/resources", self.base_url, vke_id);
        let response = self.client.get(&url).await?;

        let resources: VultrKubernetesResources = response.json().await?;
        Ok(resources)
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_available_upgrades(&self, vke_id: &str) -> Result<VultrKubernetesUpgrades> {
        let url = format!("{}/kubernetes/clusters/{}/available-upgrades", self.base_url, vke_id);
        let response = self.client.get(&url).await?;

        let upgrades: VultrKubernetesUpgrades = response.json().await?;
        Ok(upgrades)
    }

    #[instrument(skip(self, request))]
    pub async fn start_kubernetes_cluster_upgrade(&self, vke_id: &str, request: StartKubernetesUpgradeRequest) -> Result<()> {
        let url = format!("{}/kubernetes/clusters/{}/upgrades", self.base_url, vke_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to start Kubernetes upgrade: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn create_nodepool(&self, vke_id: &str, request: CreateNodePoolRequest) -> Result<VultrNodePoolDetailed> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools", self.base_url, vke_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create node pool: {}", error_text));
        }

        let nodepool: VultrNodePoolDetailed = response.json().await?;
        Ok(nodepool)
    }

    #[instrument(skip(self))]
    pub async fn get_nodepools(&self, vke_id: &str) -> Result<Vec<VultrNodePoolDetailed>> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools", self.base_url, vke_id);
        let response = self.client.get(&url).await?;

        let nodepools_response: VultrNodePoolsResponse = response.json().await?;
        Ok(nodepools_response.node_pools)
    }

    #[instrument(skip(self))]
    pub async fn get_nodepool(&self, vke_id: &str, nodepool_id: &str) -> Result<VultrNodePoolDetailed> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools/{}", self.base_url, vke_id, nodepool_id);
        let response = self.client.get(&url).await?;

        let nodepool: VultrNodePoolDetailed = response.json().await?;
        Ok(nodepool)
    }

    #[instrument(skip(self, request))]
    pub async fn update_nodepool(&self, vke_id: &str, nodepool_id: &str, request: UpdateNodePoolRequest) -> Result<VultrNodePoolDetailed> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools/{}", self.base_url, vke_id, nodepool_id);
        let response = self.client.patch(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update node pool: {}", error_text));
        }

        let nodepool: VultrNodePoolDetailed = response.json().await?;
        Ok(nodepool)
    }

    #[instrument(skip(self))]
    pub async fn delete_nodepool(&self, vke_id: &str, nodepool_id: &str) -> Result<()> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools/{}", self.base_url, vke_id, nodepool_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete node pool: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_nodepool_instance(&self, vke_id: &str, nodepool_id: &str, node_id: &str) -> Result<()> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools/{}/nodes/{}", self.base_url, vke_id, nodepool_id, node_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete node pool instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn recycle_nodepool_instance(&self, vke_id: &str, nodepool_id: &str, node_id: &str) -> Result<()> {
        let url = format!("{}/kubernetes/clusters/{}/node-pools/{}/nodes/{}/recycle", self.base_url, vke_id, nodepool_id, node_id);
        let response = self.client.post(&url).await.send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to recycle node pool instance: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_cluster_config(&self, vke_id: &str) -> Result<VultrKubernetesConfig> {
        let url = format!("{}/kubernetes/clusters/{}/config", self.base_url, vke_id);
        let response = self.client.get(&url).await?;

        let config: VultrKubernetesConfig = response.json().await?;
        Ok(config)
    }

    #[instrument(skip(self))]
    pub async fn get_kubernetes_versions(&self) -> Result<VultrKubernetesVersions> {
        let url = format!("{}/kubernetes/versions", self.base_url);
        let response = self.client.get(&url).await?;

        let versions: VultrKubernetesVersions = response.json().await?;
        Ok(versions)
    }

    // Enhanced Load Balancer endpoints
    #[instrument(skip(self))]
    pub async fn list_load_balancers(&self) -> Result<Vec<VultrLoadBalancerDetailed>> {
        let url = format!("{}/load-balancers", self.base_url);
        let response = self.client.get(&url).await?;

        let load_balancers_response: VultrLoadBalancersResponse = response.json().await?;
        Ok(load_balancers_response.load_balancers)
    }

    #[instrument(skip(self, request))]
    pub async fn create_load_balancer(&self, request: CreateLoadBalancerRequest) -> Result<VultrLoadBalancerDetailed> {
        let url = format!("{}/load-balancers", self.base_url);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create load balancer: {}", error_text));
        }

        let load_balancer: VultrLoadBalancerDetailed = response.json().await?;
        Ok(load_balancer)
    }

    #[instrument(skip(self))]
    pub async fn get_load_balancer(&self, load_balancer_id: &str) -> Result<VultrLoadBalancerDetailed> {
        let url = format!("{}/load-balancers/{}", self.base_url, load_balancer_id);
        let response = self.client.get(&url).await?;

        let load_balancer: VultrLoadBalancerDetailed = response.json().await?;
        Ok(load_balancer)
    }

    #[instrument(skip(self, request))]
    pub async fn update_load_balancer(&self, load_balancer_id: &str, request: UpdateLoadBalancerRequest) -> Result<()> {
        let url = format!("{}/load-balancers/{}", self.base_url, load_balancer_id);
        let response = self.client.patch(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to update load balancer: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_load_balancer(&self, load_balancer_id: &str) -> Result<()> {
        let url = format!("{}/load-balancers/{}", self.base_url, load_balancer_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete load balancer: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_load_balancer_ssl(&self, load_balancer_id: &str) -> Result<()> {
        let url = format!("{}/load-balancers/{}/ssl", self.base_url, load_balancer_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete load balancer SSL: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn delete_load_balancer_auto_ssl(&self, load_balancer_id: &str) -> Result<()> {
        let url = format!("{}/load-balancers/{}/ssl/auto", self.base_url, load_balancer_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete load balancer auto SSL: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self, request))]
    pub async fn create_load_balancer_forwarding_rule(&self, load_balancer_id: &str, request: CreateForwardingRuleRequest) -> Result<VultrForwardingRuleDetailed> {
        let url = format!("{}/load-balancers/{}/forwarding-rules", self.base_url, load_balancer_id);
        let response = self.client.post(&url).await.json(&request).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to create forwarding rule: {}", error_text));
        }

        let forwarding_rule: VultrForwardingRuleDetailed = response.json().await?;
        Ok(forwarding_rule)
    }

    #[instrument(skip(self))]
    pub async fn get_load_balancer_forwarding_rule(&self, load_balancer_id: &str, forwarding_rule_id: &str) -> Result<VultrForwardingRuleDetailed> {
        let url = format!("{}/load-balancers/{}/forwarding-rules/{}", self.base_url, load_balancer_id, forwarding_rule_id);
        let response = self.client.get(&url).await?;

        let forwarding_rule: VultrForwardingRuleDetailed = response.json().await?;
        Ok(forwarding_rule)
    }

    #[instrument(skip(self))]
    pub async fn delete_load_balancer_forwarding_rule(&self, load_balancer_id: &str, forwarding_rule_id: &str) -> Result<()> {
        let url = format!("{}/load-balancers/{}/forwarding-rules/{}", self.base_url, load_balancer_id, forwarding_rule_id);
        let response = self.client.delete(&url).await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("Failed to delete forwarding rule: {}", error_text));
        }

        Ok(())
    }

    #[instrument(skip(self))]
    pub async fn list_load_balancer_firewall_rules(&self, load_balancer_id: &str) -> Result<Vec<VultrLoadBalancerFirewallRule>> {
        let url = format!("{}/load-balancers/{}/firewall-rules", self.base_url, load_balancer_id);
        let response = self.client.get(&url).await?;

        let firewall_rules_response: VultrLoadBalancerFirewallRulesResponse = response.json().await?;
        Ok(firewall_rules_response.firewall_rules)
    }

    #[instrument(skip(self))]
    pub async fn get_load_balancer_firewall_rule(&self, load_balancer_id: &str, firewall_rule_id: &str) -> Result<VultrLoadBalancerFirewallRule> {
        let url = format!("{}/load-balancers/{}/firewall-rules/{}", self.base_url, load_balancer_id, firewall_rule_id);
        let response = self.client.get(&url).await?;

        let firewall_rule: VultrLoadBalancerFirewallRule = response.json().await?;
        Ok(firewall_rule)
    }
}
