use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// Account models
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VultrAccount {
    pub name: String,
    pub email: String,
    pub balance: f64,
    pub pending_charges: f64,
    pub last_payment_date: Option<String>,
    pub last_payment_amount: Option<f64>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VultrAccountResponse {
    pub account: VultrAccount,
}

// Account BGP models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrAccountBGP {
    pub bgp_available: bool,
    pub bgp_customer_count: u32,
    pub announcements: Vec<VultrBGPAnnouncement>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VultrBGPAnnouncement {
    pub id: String,
    pub description: String,
    pub ip_block: String,
    pub status: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct VultrAccountBGPResponse {
    pub bgp: VultrAccountBGP,
}

// Account Bandwidth models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrAccountBandwidth {
    pub incoming_bytes: u64,
    pub outgoing_bytes: u64,
    pub date_range: VultrBandwidthDateRange,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBandwidthDateRange {
    pub start_date: String,
    pub end_date: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrAccountBandwidthResponse {
    pub bandwidth: VultrAccountBandwidth,
}

// Instance models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInstance {
    pub id: String,
    pub os: String,
    pub ram: u32,
    pub disk: u32,
    pub main_ip: Option<String>,
    pub vcpu_count: u32,
    pub region: String,
    pub plan: String,
    pub date_created: String,
    pub status: String,
    pub allowed_bandwidth: u32,
    pub netmask_v4: Option<String>,
    pub gateway_v4: Option<String>,
    pub power_status: String,
    pub server_status: String,
    pub v6_network: Option<String>,
    pub v6_main_ip: Option<String>,
    pub v6_network_size: Option<u32>,
    pub label: String,
    pub internal_ip: Option<String>,
    pub kvm: Option<String>,
    pub hostname: Option<String>,
    pub tag: Option<String>,
    pub tags: Option<Vec<String>>,
    pub os_id: u32,
    pub app_id: Option<u32>,
    pub image_id: Option<String>,
    pub firewall_group_id: Option<String>,
    pub features: Option<Vec<String>>,
    pub user_data: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInstanceResponse {
    pub instance: VultrInstance,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInstancesResponse {
    pub instances: Vec<VultrInstance>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateVultrInstanceRequest {
    pub region: String,
    pub plan: String,
    pub os_id: Option<u32>,
    pub image_id: Option<String>,
    pub label: Option<String>,
    pub hostname: Option<String>,
    pub tag: Option<String>,
    pub user_data: Option<String>,
    pub ssh_keys: Option<Vec<String>>,
    pub startup_script_id: Option<String>,
    pub firewall_group_id: Option<String>,
    pub enable_ipv6: Option<bool>,
    pub enable_private_network: Option<bool>,
    pub attach_private_network: Option<Vec<String>>,
    pub enable_ddos_protection: Option<bool>,
    pub backups: Option<String>,
    pub app_id: Option<u32>,
    pub snapshot_id: Option<String>,
    pub iso_id: Option<String>,
    pub script_id: Option<String>,
    pub activation_email: Option<bool>,
    pub ddos_protection: Option<bool>,
    pub enable_vpc: Option<bool>,
    pub attach_vpc: Option<Vec<String>>,
    pub tags: Option<Vec<String>>,
}

// Plan models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPlan {
    pub id: String,
    pub vcpu_count: u32,
    pub ram: u32,
    pub disk: u32,
    pub bandwidth: u32,
    pub monthly_cost: f64,
    #[serde(rename = "type")]
    pub plan_type: String,
    pub locations: Vec<String>,
    pub disk_count: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPlansResponse {
    pub plans: Vec<VultrPlan>,
    pub meta: VultrMeta,
}

// Region models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegion {
    pub id: String,
    pub city: String,
    pub country: String,
    pub continent: String,
    pub options: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegionsResponse {
    pub regions: Vec<VultrRegion>,
    pub meta: VultrMeta,
}

// OS models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrOS {
    pub id: u32,
    pub name: String,
    pub arch: String,
    pub family: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrOSResponse {
    pub os: Vec<VultrOS>,
    pub meta: VultrMeta,
}

// SSH Key models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrSSHKey {
    pub id: String,
    pub name: String,
    pub ssh_key: String,
    pub date_created: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrSSHKeysResponse {
    pub ssh_keys: Vec<VultrSSHKey>,
    pub meta: VultrMeta,
}

// Common models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrMeta {
    pub total: u32,
    pub links: Option<VultrLinks>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrLinks {
    pub next: Option<String>,
    pub prev: Option<String>,
}

// Backup models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBackup {
    pub id: String,
    pub date_created: String,
    pub description: String,
    pub size: u64,
    pub status: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBackupsResponse {
    pub backups: Vec<VultrBackup>,
    pub meta: VultrMeta,
}

// Bare Metal models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetal {
    pub id: String,
    pub os: String,
    pub ram: String,
    pub disk: String,
    pub main_ip: Option<String>,
    pub cpu_count: u32,
    pub region: String,
    pub plan: String,
    pub date_created: String,
    pub status: String,
    pub label: String,
    pub tag: Option<String>,
    pub mac_address: Option<String>,
    pub netmask_v4: Option<String>,
    pub gateway_v4: Option<String>,
    pub v6_network: Option<String>,
    pub v6_main_ip: Option<String>,
    pub v6_network_size: Option<u32>,
    pub os_id: u32,
    pub app_id: Option<u32>,
    pub image_id: Option<String>,
    pub features: Vec<String>,
    pub user_data: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalResponse {
    pub bare_metals: Vec<VultrBareMetal>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateBareMetalRequest {
    pub region: String,
    pub plan: String,
    pub os_id: Option<u32>,
    pub app_id: Option<u32>,
    pub snapshot_id: Option<String>,
    pub script_id: Option<String>,
    pub enable_ipv6: Option<bool>,
    pub ssh_key_ids: Option<Vec<String>>,
    pub user_data: Option<String>,
    pub label: Option<String>,
    pub hostname: Option<String>,
    pub tag: Option<String>,
    pub reserved_ipv4: Option<String>,
    pub activation_email: Option<bool>,
    pub hostname_prefix: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateBareMetalRequest {
    pub user_data: Option<String>,
    pub label: Option<String>,
    pub tag: Option<String>,
    pub os_id: Option<u32>,
    pub app_id: Option<u32>,
    pub image_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalIpv4Info {
    pub ip: String,
    pub netmask: String,
    pub gateway: String,
    pub type_: String,
    pub reverse: String,
    pub mac_address: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalIpv6Info {
    pub ip: String,
    pub network: String,
    pub network_size: u32,
    pub type_: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalBandwidth {
    pub incoming_bytes: u64,
    pub outgoing_bytes: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalUserData {
    pub data: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalUpgrades {
    pub os: Vec<u32>,
    pub applications: Vec<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalVncInfo {
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BareMetalVpcInfo {
    pub id: String,
    pub mac_address: String,
    pub ip_address: String,
}

// CDN models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrCDN {
    pub id: String,
    pub date_created: String,
    pub status: String,
    pub label: String,
    pub origin_scheme: String,
    pub origin_domain: String,
    pub cdn_domain: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrCDNResponse {
    pub cdns: Vec<VultrCDN>,
    pub meta: VultrMeta,
}

// Container Registry models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrContainerRegistry {
    pub id: String,
    pub name: String,
    pub urn: String,
    pub storage: VultrRegistryStorage,
    pub date_created: String,
    pub public: bool,
    pub root_user: VultrRegistryUser,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryStorage {
    pub used: VultrStorageUsage,
    pub allowed: VultrStorageUsage,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStorageUsage {
    pub bytes: u64,
    pub mb: f64,
    pub gb: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrRegistryUser {
    pub id: u32,
    pub username: String,
    pub password: String,
    pub root: bool,
}

// DNS models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSDomain {
    pub domain: String,
    pub date_created: String,
    pub dns_sec: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSRecord {
    pub id: String,
    pub type_: String,
    pub name: String,
    pub data: String,
    pub priority: Option<u32>,
    pub ttl: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrDNSResponse {
    pub domains: Vec<VultrDNSDomain>,
    pub meta: VultrMeta,
}

// Firewall models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrFirewallGroup {
    pub id: String,
    pub description: String,
    pub date_created: String,
    pub date_modified: String,
    pub instance_count: u32,
    pub rule_count: u32,
    pub max_rule_count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrFirewallRule {
    pub id: u32,
    pub type_: String,
    pub ip_type: String,
    pub action: String,
    pub protocol: String,
    pub port: String,
    pub source: String,
    pub notes: Option<String>,
}

// Kubernetes models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesCluster {
    pub id: String,
    pub label: String,
    pub date_created: String,
    pub cluster_subnet: String,
    pub service_subnet: String,
    pub ip: String,
    pub endpoint: String,
    pub version: String,
    pub region: String,
    pub status: String,
    pub node_pools: Vec<VultrNodePool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrNodePool {
    pub id: String,
    pub date_created: String,
    pub date_updated: String,
    pub label: String,
    pub tag: Option<String>,
    pub plan: String,
    pub status: String,
    pub node_quantity: u32,
    pub min_nodes: u32,
    pub max_nodes: u32,
    pub auto_scaler: bool,
    pub nodes: Vec<VultrKubernetesNode>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrKubernetesNode {
    pub id: String,
    pub date_created: String,
    pub label: String,
    pub status: String,
}

// Load Balancer models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrLoadBalancer {
    pub id: String,
    pub date_created: String,
    pub region: String,
    pub label: String,
    pub status: String,
    pub ipv4: Option<String>,
    pub ipv6: Option<String>,
    pub generic_info: VultrLoadBalancerGenericInfo,
    pub health_check: VultrHealthCheck,
    pub has_ssl: bool,
    pub forwarding_rules: Vec<VultrForwardingRule>,
    pub firewall_rules: Vec<VultrFirewallRule>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrLoadBalancerGenericInfo {
    pub balancing_algorithm: String,
    pub ssl_redirect: bool,
    pub sticky_sessions: VultrStickySessions,
    pub proxy_protocol: bool,
    pub private_network: Option<String>,
    pub vpc: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStickySessions {
    pub cookie_name: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrHealthCheck {
    pub protocol: String,
    pub port: u32,
    pub path: String,
    pub check_interval: u32,
    pub response_timeout: u32,
    pub unhealthy_threshold: u32,
    pub healthy_threshold: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrForwardingRule {
    pub id: String,
    pub frontend_protocol: String,
    pub frontend_port: u32,
    pub backend_protocol: String,
    pub backend_port: u32,
}

// Error models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrError {
    pub error: String,
    pub status: u16,
}

// Managed Database models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrManagedDatabase {
    pub id: String,
    pub date_created: String,
    pub plan: String,
    pub plan_disk: u32,
    pub plan_ram: u32,
    pub plan_vcpus: u32,
    pub plan_replicas: u32,
    pub region: String,
    pub database_engine: String,
    pub database_engine_version: String,
    pub vpc_id: Option<String>,
    pub status: String,
    pub label: String,
    pub tag: Option<String>,
    pub dbname: String,
    pub host: String,
    pub public_host: Option<String>,
    pub user: String,
    pub password: String,
    pub port: String,
    pub maintenance_dow: String,
    pub maintenance_time: String,
    pub latest_backup: Option<String>,
    pub trusted_ips: Vec<String>,
    pub mysql_sql_modes: Option<Vec<String>>,
    pub mysql_require_primary_key: Option<bool>,
    pub mysql_slow_query_log: Option<bool>,
    pub mysql_long_query_time: Option<u32>,
    pub redis_eviction_policy: Option<String>,
}

// Object Storage models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrObjectStorage {
    pub id: String,
    pub date_created: String,
    pub cluster_id: u32,
    pub region: String,
    pub label: String,
    pub status: String,
    pub s3_hostname: String,
    pub s3_access_key: String,
    pub s3_secret_key: String,
}

// VPC models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVPC {
    pub id: String,
    pub date_created: String,
    pub region: String,
    pub description: String,
    pub v4_subnet: String,
    pub v4_subnet_mask: u32,
}

// Block Storage models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBlockStorage {
    pub id: String,
    pub date_created: String,
    pub cost_per_month: f64,
    pub status: String,
    pub size_gb: u32,
    pub region: String,
    pub attached_to_instance: Option<String>,
    pub label: String,
    pub mount_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBlockStorageResponse {
    pub blocks: Vec<VultrBlockStorage>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateBlockStorageRequest {
    pub region: String,
    pub size_gb: u32,
    pub label: Option<String>,
    pub block_type: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateBlockStorageRequest {
    pub label: Option<String>,
    pub size_gb: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttachBlockStorageRequest {
    pub instance_id: String,
    pub live: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetachBlockStorageRequest {
    pub live: Option<bool>,
}

// Billing models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInvoiceItem {
    pub description: String,
    pub product: String,
    pub start_date: String,
    pub end_date: String,
    pub units: f64,
    pub unit_type: String,
    pub unit_price: f64,
    pub total: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInvoiceItemsResponse {
    pub invoice_items: Vec<VultrInvoiceItem>,
    pub meta: VultrMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPendingCharges {
    pub pending_charges: f64,
    pub billing_date: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrPendingChargesResponse {
    pub billing: VultrPendingCharges,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrVPC2 {
    pub id: String,
    pub date_created: String,
    pub region: String,
    pub description: String,
    pub ip_block: String,
    pub prefix_length: u32,
}

// Reserved IP models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrReservedIP {
    pub id: String,
    pub region: String,
    pub ip_type: String,
    pub subnet: String,
    pub subnet_size: u32,
    pub label: String,
    pub instance_id: Option<String>,
}

// Snapshot models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrSnapshot {
    pub id: String,
    pub date_created: String,
    pub description: String,
    pub size: u64,
    pub compressed_size: u64,
    pub status: String,
    pub os_id: u32,
    pub app_id: Option<u32>,
}

// Sub-Account models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrSubAccount {
    pub id: String,
    pub name: String,
    pub email: String,
    pub activated: bool,
    pub balance: f64,
    pub pending_charges: f64,
}

// Startup Script models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStartupScript {
    pub id: String,
    pub date_created: String,
    pub date_modified: String,
    pub name: String,
    pub script: String,
    #[serde(rename = "type")]
    pub script_type: String,
}

// ISO models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrISO {
    pub id: String,
    pub date_created: String,
    pub filename: String,
    pub size: u64,
    pub md5sum: String,
    pub sha512sum: String,
    pub status: String,
}

// Application models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrApplication {
    pub id: u32,
    pub name: String,
    pub short_name: String,
    pub deploy_name: String,
    pub surcharge: f64,
    pub vendor: String,
    pub image_id: String,
}

// Billing models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBillingHistory {
    pub id: u32,
    pub date: String,
    pub type_: String,
    pub description: String,
    pub amount: f64,
    pub balance: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrInvoice {
    pub id: u32,
    pub date: String,
    pub description: String,
    pub amount: f64,
    pub balance: f64,
}

// Marketplace models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrMarketplaceApp {
    pub id: u32,
    pub name: String,
    pub short_name: String,
    pub deploy_name: String,
    pub surcharge: f64,
    pub vendor: String,
    pub image_id: String,
    pub type_: String,
}

// Storage Gateway models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrStorageGateway {
    pub id: String,
    pub date_created: String,
    pub region: String,
    pub label: String,
    pub status: String,
    pub hostname: String,
    pub port: u32,
    pub username: String,
    pub password: String,
}

// User models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrUser {
    pub id: String,
    pub name: String,
    pub email: String,
    pub api_enabled: bool,
    pub acls: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrErrorResponse {
    pub error: VultrError,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalIpv4Response {
    pub ipv4s: Vec<BareMetalIpv4Info>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalIpv6Response {
    pub ipv6s: Vec<BareMetalIpv6Info>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalBandwidthResponse {
    pub bandwidth: BareMetalBandwidth,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalUserDataResponse {
    pub user_data: BareMetalUserData,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalUpgradesResponse {
    pub upgrades: BareMetalUpgrades,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalVncResponse {
    pub vnc: BareMetalVncInfo,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VultrBareMetalVpcsResponse {
    pub vpcs: Vec<BareMetalVpcInfo>,
    pub meta: VultrMeta,
}
